const CHUNK_PUBLIC_PATH = "server/app/api/improve/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_d095c0d5._.js");
runtime.loadChunk("server/chunks/node_modules_acorn_dist_acorn_mjs_69fe48d7._.js");
runtime.loadChunk("server/chunks/node_modules_@babel_parser_lib_index_bf197025.js");
runtime.loadChunk("server/chunks/node_modules_openai_1a6eb0e6._.js");
runtime.loadChunk("server/chunks/node_modules_b6e2c493._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__3fa81919._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/improve/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/improve/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/improve/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
