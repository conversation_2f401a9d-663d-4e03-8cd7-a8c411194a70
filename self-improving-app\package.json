{"name": "self-improving-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/parser": "^7.26.2", "@types/acorn": "^4.0.6", "@types/fs-extra": "^11.0.4", "@types/react-syntax-highlighter": "^15.5.13", "acorn": "^8.14.1", "fs-extra": "^11.3.0", "lucide-react": "^0.513.0", "next": "15.3.3", "openai": "^5.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.17.57", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "typescript": "^5"}}