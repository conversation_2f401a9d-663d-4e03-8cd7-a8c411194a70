import OpenAI from 'openai';

export interface CodeAnalysisResult {
  issues: CodeIssue[];
  suggestions: CodeSuggestion[];
  metrics: CodeMetrics;
}

export interface CodeIssue {
  type: 'error' | 'warning' | 'info';
  message: string;
  file: string;
  line?: number;
  column?: number;
  severity: number; // 1-10
}

export interface CodeSuggestion {
  id: string;
  type: 'performance' | 'refactor' | 'feature' | 'bug-fix' | 'style';
  title: string;
  description: string;
  file: string;
  originalCode: string;
  suggestedCode: string;
  confidence: number; // 0-1
  impact: 'low' | 'medium' | 'high';
  estimatedBenefit: string;
}

export interface CodeMetrics {
  linesOfCode: number;
  complexity: number;
  maintainability: number;
  testCoverage?: number;
  performance: number;
}

export class AIClient {
  private openai: OpenAI;

  constructor() {
    if (!process.env.OPENAI_API_KEY) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }
    
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }

  async analyzeCode(code: string, fileName: string): Promise<CodeAnalysisResult> {
    const prompt = this.createAnalysisPrompt(code, fileName);
    
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert code analyzer. Analyze the provided code and return a JSON response with issues, suggestions, and metrics.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new Error('No response from OpenAI');
      }

      return this.parseAnalysisResponse(content, fileName);
    } catch (error) {
      console.error('Error analyzing code:', error);
      return this.createEmptyAnalysis();
    }
  }

  async generateImprovement(
    code: string, 
    fileName: string, 
    improvementType: string,
    context?: string
  ): Promise<CodeSuggestion | null> {
    const prompt = this.createImprovementPrompt(code, fileName, improvementType, context);
    
    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4.1-mini-2025-04-14',
        messages: [
          {
            role: 'system',
            content: 'You are an expert software engineer. Generate code improvements based on the request and return a JSON response.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.4,
        max_tokens: 1500,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        return null;
      }

      return this.parseImprovementResponse(content, fileName);
    } catch (error) {
      console.error('Error generating improvement:', error);
      return null;
    }
  }

  async generateNewFeature(
    description: string,
    existingCode: string,
    fileName: string
  ): Promise<CodeSuggestion | null> {
    const prompt = `
Generate a new feature based on this description: "${description}"

Existing code context:
\`\`\`${this.getFileExtension(fileName)}
${existingCode}
\`\`\`

Please provide:
1. A clear implementation plan
2. The new code to add
3. Any modifications needed to existing code
4. Potential impacts and considerations

Return your response as JSON with this structure:
{
  "title": "Feature title",
  "description": "Detailed description",
  "originalCode": "existing code that needs modification",
  "suggestedCode": "new/modified code",
  "confidence": 0.8,
  "impact": "medium",
  "estimatedBenefit": "description of benefits"
}
`;

    try {
      const response = await this.openai.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert software engineer specializing in feature development.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.5,
        max_tokens: 2000,
      });

      const content = response.choices[0]?.message?.content;
      if (!content) {
        return null;
      }

      return this.parseImprovementResponse(content, fileName, 'feature');
    } catch (error) {
      console.error('Error generating new feature:', error);
      return null;
    }
  }

  private createAnalysisPrompt(code: string, fileName: string): string {
    return `
Analyze this ${this.getFileExtension(fileName)} code for issues, improvements, and metrics:

File: ${fileName}
\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

Please provide a JSON response with this structure:
{
  "issues": [
    {
      "type": "error|warning|info",
      "message": "description",
      "line": 10,
      "severity": 5
    }
  ],
  "suggestions": [
    {
      "type": "performance|refactor|bug-fix|style",
      "title": "suggestion title",
      "description": "detailed description",
      "originalCode": "code to replace",
      "suggestedCode": "replacement code",
      "confidence": 0.8,
      "impact": "low|medium|high",
      "estimatedBenefit": "description of benefits"
    }
  ],
  "metrics": {
    "linesOfCode": 50,
    "complexity": 3,
    "maintainability": 8,
    "performance": 7
  }
}
`;
  }

  private createImprovementPrompt(
    code: string, 
    fileName: string, 
    improvementType: string,
    context?: string
  ): string {
    return `
Generate a ${improvementType} improvement for this code:

File: ${fileName}
${context ? `Context: ${context}` : ''}

\`\`\`${this.getFileExtension(fileName)}
${code}
\`\`\`

Focus on ${improvementType} improvements and return JSON with:
{
  "title": "improvement title",
  "description": "detailed description",
  "originalCode": "code to replace",
  "suggestedCode": "improved code",
  "confidence": 0.8,
  "impact": "low|medium|high",
  "estimatedBenefit": "description of benefits"
}
`;
  }

  private parseAnalysisResponse(content: string, fileName: string): CodeAnalysisResult {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      
      // Add file name to all issues and suggestions
      if (parsed.issues) {
        parsed.issues.forEach((issue: any) => {
          issue.file = fileName;
        });
      }
      
      if (parsed.suggestions) {
        parsed.suggestions.forEach((suggestion: any) => {
          suggestion.file = fileName;
          suggestion.id = this.generateId();
        });
      }
      
      return parsed;
    } catch (error) {
      console.error('Error parsing analysis response:', error);
      return this.createEmptyAnalysis();
    }
  }

  private parseImprovementResponse(
    content: string, 
    fileName: string, 
    type: string = 'refactor'
  ): CodeSuggestion | null {
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        return null;
      }
      
      const parsed = JSON.parse(jsonMatch[0]);
      
      return {
        id: this.generateId(),
        type: type as any,
        file: fileName,
        ...parsed
      };
    } catch (error) {
      console.error('Error parsing improvement response:', error);
      return null;
    }
  }

  private createEmptyAnalysis(): CodeAnalysisResult {
    return {
      issues: [],
      suggestions: [],
      metrics: {
        linesOfCode: 0,
        complexity: 0,
        maintainability: 0,
        performance: 0
      }
    };
  }

  private getFileExtension(fileName: string): string {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'ts':
      case 'tsx':
        return 'typescript';
      case 'js':
      case 'jsx':
        return 'javascript';
      case 'css':
        return 'css';
      case 'json':
        return 'json';
      default:
        return 'text';
    }
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const aiClient = new AIClient();
