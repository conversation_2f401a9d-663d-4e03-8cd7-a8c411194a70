{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/universalify/index.js"], "sourcesContent": ["'use strict'\n\nexports.fromCallback = function (fn) {\n  return Object.defineProperty(function (...args) {\n    if (typeof args[args.length - 1] === 'function') fn.apply(this, args)\n    else {\n      return new Promise((resolve, reject) => {\n        args.push((err, res) => (err != null) ? reject(err) : resolve(res))\n        fn.apply(this, args)\n      })\n    }\n  }, 'name', { value: fn.name })\n}\n\nexports.fromPromise = function (fn) {\n  return Object.defineProperty(function (...args) {\n    const cb = args[args.length - 1]\n    if (typeof cb !== 'function') return fn.apply(this, args)\n    else {\n      args.pop()\n      fn.apply(this, args).then(r => cb(null, r), cb)\n    }\n  }, 'name', { value: fn.name })\n}\n"], "names": [], "mappings": "AAAA;AAEA,QAAQ,YAAY,GAAG,SAAU,EAAE;IACjC,OAAO,OAAO,cAAc,CAAC,SAAU,GAAG,IAAI;QAC5C,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,YAAY,GAAG,KAAK,CAAC,IAAI,EAAE;aAC3D;YACH,OAAO,IAAI,QAAQ,CAAC,SAAS;gBAC3B,KAAK,IAAI,CAAC,CAAC,KAAK,MAAQ,AAAC,OAAO,OAAQ,OAAO,OAAO,QAAQ;gBAC9D,GAAG,KAAK,CAAC,IAAI,EAAE;YACjB;QACF;IACF,GAAG,QAAQ;QAAE,OAAO,GAAG,IAAI;IAAC;AAC9B;AAEA,QAAQ,WAAW,GAAG,SAAU,EAAE;IAChC,OAAO,OAAO,cAAc,CAAC,SAAU,GAAG,IAAI;QAC5C,MAAM,KAAK,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAChC,IAAI,OAAO,OAAO,YAAY,OAAO,GAAG,KAAK,CAAC,IAAI,EAAE;aAC/C;YACH,KAAK,GAAG;YACR,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,CAAA,IAAK,GAAG,MAAM,IAAI;QAC9C;IACF,GAAG,QAAQ;QAAE,OAAO,GAAG,IAAI;IAAC;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/graceful-fs/polyfills.js"], "sourcesContent": ["var constants = require('constants')\n\nvar origCwd = process.cwd\nvar cwd = null\n\nvar platform = process.env.GRACEFUL_FS_PLATFORM || process.platform\n\nprocess.cwd = function() {\n  if (!cwd)\n    cwd = origCwd.call(process)\n  return cwd\n}\ntry {\n  process.cwd()\n} catch (er) {}\n\n// This check is needed until node.js 12 is required\nif (typeof process.chdir === 'function') {\n  var chdir = process.chdir\n  process.chdir = function (d) {\n    cwd = null\n    chdir.call(process, d)\n  }\n  if (Object.setPrototypeOf) Object.setPrototypeOf(process.chdir, chdir)\n}\n\nmodule.exports = patch\n\nfunction patch (fs) {\n  // (re-)implement some things that are known busted or missing.\n\n  // lchmod, broken prior to 0.6.2\n  // back-port the fix here.\n  if (constants.hasOwnProperty('O_SYMLINK') &&\n      process.version.match(/^v0\\.6\\.[0-2]|^v0\\.5\\./)) {\n    patchLchmod(fs)\n  }\n\n  // lutimes implementation, or no-op\n  if (!fs.lutimes) {\n    patchLutimes(fs)\n  }\n\n  // https://github.com/isaacs/node-graceful-fs/issues/4\n  // Chown should not fail on einval or eperm if non-root.\n  // It should not fail on enosys ever, as this just indicates\n  // that a fs doesn't support the intended operation.\n\n  fs.chown = chownFix(fs.chown)\n  fs.fchown = chownFix(fs.fchown)\n  fs.lchown = chownFix(fs.lchown)\n\n  fs.chmod = chmodFix(fs.chmod)\n  fs.fchmod = chmodFix(fs.fchmod)\n  fs.lchmod = chmodFix(fs.lchmod)\n\n  fs.chownSync = chownFixSync(fs.chownSync)\n  fs.fchownSync = chownFixSync(fs.fchownSync)\n  fs.lchownSync = chownFixSync(fs.lchownSync)\n\n  fs.chmodSync = chmodFixSync(fs.chmodSync)\n  fs.fchmodSync = chmodFixSync(fs.fchmodSync)\n  fs.lchmodSync = chmodFixSync(fs.lchmodSync)\n\n  fs.stat = statFix(fs.stat)\n  fs.fstat = statFix(fs.fstat)\n  fs.lstat = statFix(fs.lstat)\n\n  fs.statSync = statFixSync(fs.statSync)\n  fs.fstatSync = statFixSync(fs.fstatSync)\n  fs.lstatSync = statFixSync(fs.lstatSync)\n\n  // if lchmod/lchown do not exist, then make them no-ops\n  if (fs.chmod && !fs.lchmod) {\n    fs.lchmod = function (path, mode, cb) {\n      if (cb) process.nextTick(cb)\n    }\n    fs.lchmodSync = function () {}\n  }\n  if (fs.chown && !fs.lchown) {\n    fs.lchown = function (path, uid, gid, cb) {\n      if (cb) process.nextTick(cb)\n    }\n    fs.lchownSync = function () {}\n  }\n\n  // on Windows, A/V software can lock the directory, causing this\n  // to fail with an EACCES or EPERM if the directory contains newly\n  // created files.  Try again on failure, for up to 60 seconds.\n\n  // Set the timeout this long because some Windows Anti-Virus, such as Parity\n  // bit9, may lock files for up to a minute, causing npm package install\n  // failures. Also, take care to yield the scheduler. Windows scheduling gives\n  // CPU to a busy looping process, which can cause the program causing the lock\n  // contention to be starved of CPU by node, so the contention doesn't resolve.\n  if (platform === \"win32\") {\n    fs.rename = typeof fs.rename !== 'function' ? fs.rename\n    : (function (fs$rename) {\n      function rename (from, to, cb) {\n        var start = Date.now()\n        var backoff = 0;\n        fs$rename(from, to, function CB (er) {\n          if (er\n              && (er.code === \"EACCES\" || er.code === \"EPERM\" || er.code === \"EBUSY\")\n              && Date.now() - start < 60000) {\n            setTimeout(function() {\n              fs.stat(to, function (stater, st) {\n                if (stater && stater.code === \"ENOENT\")\n                  fs$rename(from, to, CB);\n                else\n                  cb(er)\n              })\n            }, backoff)\n            if (backoff < 100)\n              backoff += 10;\n            return;\n          }\n          if (cb) cb(er)\n        })\n      }\n      if (Object.setPrototypeOf) Object.setPrototypeOf(rename, fs$rename)\n      return rename\n    })(fs.rename)\n  }\n\n  // if read() returns EAGAIN, then just try it again.\n  fs.read = typeof fs.read !== 'function' ? fs.read\n  : (function (fs$read) {\n    function read (fd, buffer, offset, length, position, callback_) {\n      var callback\n      if (callback_ && typeof callback_ === 'function') {\n        var eagCounter = 0\n        callback = function (er, _, __) {\n          if (er && er.code === 'EAGAIN' && eagCounter < 10) {\n            eagCounter ++\n            return fs$read.call(fs, fd, buffer, offset, length, position, callback)\n          }\n          callback_.apply(this, arguments)\n        }\n      }\n      return fs$read.call(fs, fd, buffer, offset, length, position, callback)\n    }\n\n    // This ensures `util.promisify` works as it does for native `fs.read`.\n    if (Object.setPrototypeOf) Object.setPrototypeOf(read, fs$read)\n    return read\n  })(fs.read)\n\n  fs.readSync = typeof fs.readSync !== 'function' ? fs.readSync\n  : (function (fs$readSync) { return function (fd, buffer, offset, length, position) {\n    var eagCounter = 0\n    while (true) {\n      try {\n        return fs$readSync.call(fs, fd, buffer, offset, length, position)\n      } catch (er) {\n        if (er.code === 'EAGAIN' && eagCounter < 10) {\n          eagCounter ++\n          continue\n        }\n        throw er\n      }\n    }\n  }})(fs.readSync)\n\n  function patchLchmod (fs) {\n    fs.lchmod = function (path, mode, callback) {\n      fs.open( path\n             , constants.O_WRONLY | constants.O_SYMLINK\n             , mode\n             , function (err, fd) {\n        if (err) {\n          if (callback) callback(err)\n          return\n        }\n        // prefer to return the chmod error, if one occurs,\n        // but still try to close, and report closing errors if they occur.\n        fs.fchmod(fd, mode, function (err) {\n          fs.close(fd, function(err2) {\n            if (callback) callback(err || err2)\n          })\n        })\n      })\n    }\n\n    fs.lchmodSync = function (path, mode) {\n      var fd = fs.openSync(path, constants.O_WRONLY | constants.O_SYMLINK, mode)\n\n      // prefer to return the chmod error, if one occurs,\n      // but still try to close, and report closing errors if they occur.\n      var threw = true\n      var ret\n      try {\n        ret = fs.fchmodSync(fd, mode)\n        threw = false\n      } finally {\n        if (threw) {\n          try {\n            fs.closeSync(fd)\n          } catch (er) {}\n        } else {\n          fs.closeSync(fd)\n        }\n      }\n      return ret\n    }\n  }\n\n  function patchLutimes (fs) {\n    if (constants.hasOwnProperty(\"O_SYMLINK\") && fs.futimes) {\n      fs.lutimes = function (path, at, mt, cb) {\n        fs.open(path, constants.O_SYMLINK, function (er, fd) {\n          if (er) {\n            if (cb) cb(er)\n            return\n          }\n          fs.futimes(fd, at, mt, function (er) {\n            fs.close(fd, function (er2) {\n              if (cb) cb(er || er2)\n            })\n          })\n        })\n      }\n\n      fs.lutimesSync = function (path, at, mt) {\n        var fd = fs.openSync(path, constants.O_SYMLINK)\n        var ret\n        var threw = true\n        try {\n          ret = fs.futimesSync(fd, at, mt)\n          threw = false\n        } finally {\n          if (threw) {\n            try {\n              fs.closeSync(fd)\n            } catch (er) {}\n          } else {\n            fs.closeSync(fd)\n          }\n        }\n        return ret\n      }\n\n    } else if (fs.futimes) {\n      fs.lutimes = function (_a, _b, _c, cb) { if (cb) process.nextTick(cb) }\n      fs.lutimesSync = function () {}\n    }\n  }\n\n  function chmodFix (orig) {\n    if (!orig) return orig\n    return function (target, mode, cb) {\n      return orig.call(fs, target, mode, function (er) {\n        if (chownErOk(er)) er = null\n        if (cb) cb.apply(this, arguments)\n      })\n    }\n  }\n\n  function chmodFixSync (orig) {\n    if (!orig) return orig\n    return function (target, mode) {\n      try {\n        return orig.call(fs, target, mode)\n      } catch (er) {\n        if (!chownErOk(er)) throw er\n      }\n    }\n  }\n\n\n  function chownFix (orig) {\n    if (!orig) return orig\n    return function (target, uid, gid, cb) {\n      return orig.call(fs, target, uid, gid, function (er) {\n        if (chownErOk(er)) er = null\n        if (cb) cb.apply(this, arguments)\n      })\n    }\n  }\n\n  function chownFixSync (orig) {\n    if (!orig) return orig\n    return function (target, uid, gid) {\n      try {\n        return orig.call(fs, target, uid, gid)\n      } catch (er) {\n        if (!chownErOk(er)) throw er\n      }\n    }\n  }\n\n  function statFix (orig) {\n    if (!orig) return orig\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options, cb) {\n      if (typeof options === 'function') {\n        cb = options\n        options = null\n      }\n      function callback (er, stats) {\n        if (stats) {\n          if (stats.uid < 0) stats.uid += 0x100000000\n          if (stats.gid < 0) stats.gid += 0x100000000\n        }\n        if (cb) cb.apply(this, arguments)\n      }\n      return options ? orig.call(fs, target, options, callback)\n        : orig.call(fs, target, callback)\n    }\n  }\n\n  function statFixSync (orig) {\n    if (!orig) return orig\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options) {\n      var stats = options ? orig.call(fs, target, options)\n        : orig.call(fs, target)\n      if (stats) {\n        if (stats.uid < 0) stats.uid += 0x100000000\n        if (stats.gid < 0) stats.gid += 0x100000000\n      }\n      return stats;\n    }\n  }\n\n  // ENOSYS means that the fs doesn't support the op. Just ignore\n  // that, because it doesn't matter.\n  //\n  // if there's no getuid, or if getuid() is something other\n  // than 0, and the error is EINVAL or EPERM, then just ignore\n  // it.\n  //\n  // This specific case is a silent failure in cp, install, tar,\n  // and most other unix tools that manage permissions.\n  //\n  // When running as root, or if other types of errors are\n  // encountered, then it's strict.\n  function chownErOk (er) {\n    if (!er)\n      return true\n\n    if (er.code === \"ENOSYS\")\n      return true\n\n    var nonroot = !process.getuid || process.getuid() !== 0\n    if (nonroot) {\n      if (er.code === \"EINVAL\" || er.code === \"EPERM\")\n        return true\n    }\n\n    return false\n  }\n}\n"], "names": [], "mappings": "AAAA,IAAI;AAEJ,IAAI,UAAU,QAAQ,GAAG;AACzB,IAAI,MAAM;AAEV,IAAI,WAAW,QAAQ,GAAG,CAAC,oBAAoB,IAAI,QAAQ,QAAQ;AAEnE,QAAQ,GAAG,GAAG;IACZ,IAAI,CAAC,KACH,MAAM,QAAQ,IAAI,CAAC;IACrB,OAAO;AACT;AACA,IAAI;IACF,QAAQ,GAAG;AACb,EAAE,OAAO,IAAI,CAAC;AAEd,oDAAoD;AACpD,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;IACvC,IAAI,QAAQ,QAAQ,KAAK;IACzB,QAAQ,KAAK,GAAG,SAAU,CAAC;QACzB,MAAM;QACN,MAAM,IAAI,CAAC,SAAS;IACtB;IACA,IAAI,OAAO,cAAc,EAAE,OAAO,cAAc,CAAC,QAAQ,KAAK,EAAE;AAClE;AAEA,OAAO,OAAO,GAAG;AAEjB,SAAS,MAAO,EAAE;IAChB,+DAA+D;IAE/D,gCAAgC;IAChC,0BAA0B;IAC1B,IAAI,UAAU,cAAc,CAAC,gBACzB,QAAQ,OAAO,CAAC,KAAK,CAAC,2BAA2B;QACnD,YAAY;IACd;IAEA,mCAAmC;IACnC,IAAI,CAAC,GAAG,OAAO,EAAE;QACf,aAAa;IACf;IAEA,sDAAsD;IACtD,wDAAwD;IACxD,4DAA4D;IAC5D,oDAAoD;IAEpD,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK;IAC5B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAC9B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAE9B,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK;IAC5B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAC9B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAE9B,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS;IACxC,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAC1C,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAE1C,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS;IACxC,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAC1C,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAE1C,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI;IACzB,GAAG,KAAK,GAAG,QAAQ,GAAG,KAAK;IAC3B,GAAG,KAAK,GAAG,QAAQ,GAAG,KAAK;IAE3B,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ;IACrC,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS;IACvC,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS;IAEvC,uDAAuD;IACvD,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE;QAC1B,GAAG,MAAM,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,IAAI,QAAQ,QAAQ,CAAC;QAC3B;QACA,GAAG,UAAU,GAAG,YAAa;IAC/B;IACA,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE;QAC1B,GAAG,MAAM,GAAG,SAAU,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACtC,IAAI,IAAI,QAAQ,QAAQ,CAAC;QAC3B;QACA,GAAG,UAAU,GAAG,YAAa;IAC/B;IAEA,gEAAgE;IAChE,kEAAkE;IAClE,8DAA8D;IAE9D,4EAA4E;IAC5E,uEAAuE;IACvE,6EAA6E;IAC7E,8EAA8E;IAC9E,8EAA8E;IAC9E,IAAI,aAAa,SAAS;QACxB,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,KAAK,aAAa,GAAG,MAAM,GACrD,AAAC,SAAU,SAAS;YACpB,SAAS,OAAQ,IAAI,EAAE,EAAE,EAAE,EAAE;gBAC3B,IAAI,QAAQ,KAAK,GAAG;gBACpB,IAAI,UAAU;gBACd,UAAU,MAAM,IAAI,SAAS,GAAI,EAAE;oBACjC,IAAI,MACG,CAAC,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,WAAW,GAAG,IAAI,KAAK,OAAO,KACnE,KAAK,GAAG,KAAK,QAAQ,OAAO;wBACjC,WAAW;4BACT,GAAG,IAAI,CAAC,IAAI,SAAU,MAAM,EAAE,EAAE;gCAC9B,IAAI,UAAU,OAAO,IAAI,KAAK,UAC5B,UAAU,MAAM,IAAI;qCAEpB,GAAG;4BACP;wBACF,GAAG;wBACH,IAAI,UAAU,KACZ,WAAW;wBACb;oBACF;oBACA,IAAI,IAAI,GAAG;gBACb;YACF;YACA,IAAI,OAAO,cAAc,EAAE,OAAO,cAAc,CAAC,QAAQ;YACzD,OAAO;QACT,EAAG,GAAG,MAAM;IACd;IAEA,oDAAoD;IACpD,GAAG,IAAI,GAAG,OAAO,GAAG,IAAI,KAAK,aAAa,GAAG,IAAI,GAC/C,AAAC,SAAU,OAAO;QAClB,SAAS,KAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS;YAC5D,IAAI;YACJ,IAAI,aAAa,OAAO,cAAc,YAAY;gBAChD,IAAI,aAAa;gBACjB,WAAW,SAAU,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC5B,IAAI,MAAM,GAAG,IAAI,KAAK,YAAY,aAAa,IAAI;wBACjD;wBACA,OAAO,QAAQ,IAAI,CAAC,IAAI,IAAI,QAAQ,QAAQ,QAAQ,UAAU;oBAChE;oBACA,UAAU,KAAK,CAAC,IAAI,EAAE;gBACxB;YACF;YACA,OAAO,QAAQ,IAAI,CAAC,IAAI,IAAI,QAAQ,QAAQ,QAAQ,UAAU;QAChE;QAEA,uEAAuE;QACvE,IAAI,OAAO,cAAc,EAAE,OAAO,cAAc,CAAC,MAAM;QACvD,OAAO;IACT,EAAG,GAAG,IAAI;IAEV,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,KAAK,aAAa,GAAG,QAAQ,GAC3D,AAAC,SAAU,WAAW;QAAI,OAAO,SAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;YAC/E,IAAI,aAAa;YACjB,MAAO,KAAM;gBACX,IAAI;oBACF,OAAO,YAAY,IAAI,CAAC,IAAI,IAAI,QAAQ,QAAQ,QAAQ;gBAC1D,EAAE,OAAO,IAAI;oBACX,IAAI,GAAG,IAAI,KAAK,YAAY,aAAa,IAAI;wBAC3C;wBACA;oBACF;oBACA,MAAM;gBACR;YACF;QACF;IAAC,EAAG,GAAG,QAAQ;IAEf,SAAS,YAAa,EAAE;QACtB,GAAG,MAAM,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,QAAQ;YACxC,GAAG,IAAI,CAAE,MACA,UAAU,QAAQ,GAAG,UAAU,SAAS,EACxC,MACA,SAAU,GAAG,EAAE,EAAE;gBACxB,IAAI,KAAK;oBACP,IAAI,UAAU,SAAS;oBACvB;gBACF;gBACA,mDAAmD;gBACnD,mEAAmE;gBACnE,GAAG,MAAM,CAAC,IAAI,MAAM,SAAU,GAAG;oBAC/B,GAAG,KAAK,CAAC,IAAI,SAAS,IAAI;wBACxB,IAAI,UAAU,SAAS,OAAO;oBAChC;gBACF;YACF;QACF;QAEA,GAAG,UAAU,GAAG,SAAU,IAAI,EAAE,IAAI;YAClC,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,UAAU,QAAQ,GAAG,UAAU,SAAS,EAAE;YAErE,mDAAmD;YACnD,mEAAmE;YACnE,IAAI,QAAQ;YACZ,IAAI;YACJ,IAAI;gBACF,MAAM,GAAG,UAAU,CAAC,IAAI;gBACxB,QAAQ;YACV,SAAU;gBACR,IAAI,OAAO;oBACT,IAAI;wBACF,GAAG,SAAS,CAAC;oBACf,EAAE,OAAO,IAAI,CAAC;gBAChB,OAAO;oBACL,GAAG,SAAS,CAAC;gBACf;YACF;YACA,OAAO;QACT;IACF;IAEA,SAAS,aAAc,EAAE;QACvB,IAAI,UAAU,cAAc,CAAC,gBAAgB,GAAG,OAAO,EAAE;YACvD,GAAG,OAAO,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBACrC,GAAG,IAAI,CAAC,MAAM,UAAU,SAAS,EAAE,SAAU,EAAE,EAAE,EAAE;oBACjD,IAAI,IAAI;wBACN,IAAI,IAAI,GAAG;wBACX;oBACF;oBACA,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,SAAU,EAAE;wBACjC,GAAG,KAAK,CAAC,IAAI,SAAU,GAAG;4BACxB,IAAI,IAAI,GAAG,MAAM;wBACnB;oBACF;gBACF;YACF;YAEA,GAAG,WAAW,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,EAAE;gBACrC,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,UAAU,SAAS;gBAC9C,IAAI;gBACJ,IAAI,QAAQ;gBACZ,IAAI;oBACF,MAAM,GAAG,WAAW,CAAC,IAAI,IAAI;oBAC7B,QAAQ;gBACV,SAAU;oBACR,IAAI,OAAO;wBACT,IAAI;4BACF,GAAG,SAAS,CAAC;wBACf,EAAE,OAAO,IAAI,CAAC;oBAChB,OAAO;wBACL,GAAG,SAAS,CAAC;oBACf;gBACF;gBACA,OAAO;YACT;QAEF,OAAO,IAAI,GAAG,OAAO,EAAE;YACrB,GAAG,OAAO,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAAI,IAAI,IAAI,QAAQ,QAAQ,CAAC;YAAI;YACtE,GAAG,WAAW,GAAG,YAAa;QAChC;IACF;IAEA,SAAS,SAAU,IAAI;QACrB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,IAAI,EAAE,EAAE;YAC/B,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ,MAAM,SAAU,EAAE;gBAC7C,IAAI,UAAU,KAAK,KAAK;gBACxB,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;YACzB;QACF;IACF;IAEA,SAAS,aAAc,IAAI;QACzB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,IAAI;YAC3B,IAAI;gBACF,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ;YAC/B,EAAE,OAAO,IAAI;gBACX,IAAI,CAAC,UAAU,KAAK,MAAM;YAC5B;QACF;IACF;IAGA,SAAS,SAAU,IAAI;QACrB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACnC,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK,SAAU,EAAE;gBACjD,IAAI,UAAU,KAAK,KAAK;gBACxB,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;YACzB;QACF;IACF;IAEA,SAAS,aAAc,IAAI;QACzB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,GAAG,EAAE,GAAG;YAC/B,IAAI;gBACF,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ,KAAK;YACpC,EAAE,OAAO,IAAI;gBACX,IAAI,CAAC,UAAU,KAAK,MAAM;YAC5B;QACF;IACF;IAEA,SAAS,QAAS,IAAI;QACpB,IAAI,CAAC,MAAM,OAAO;QAClB,kEAAkE;QAClE,aAAa;QACb,OAAO,SAAU,MAAM,EAAE,OAAO,EAAE,EAAE;YAClC,IAAI,OAAO,YAAY,YAAY;gBACjC,KAAK;gBACL,UAAU;YACZ;YACA,SAAS,SAAU,EAAE,EAAE,KAAK;gBAC1B,IAAI,OAAO;oBACT,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;oBAChC,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;gBAClC;gBACA,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;YACzB;YACA,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,QAAQ,SAAS,YAC5C,KAAK,IAAI,CAAC,IAAI,QAAQ;QAC5B;IACF;IAEA,SAAS,YAAa,IAAI;QACxB,IAAI,CAAC,MAAM,OAAO;QAClB,kEAAkE;QAClE,aAAa;QACb,OAAO,SAAU,MAAM,EAAE,OAAO;YAC9B,IAAI,QAAQ,UAAU,KAAK,IAAI,CAAC,IAAI,QAAQ,WACxC,KAAK,IAAI,CAAC,IAAI;YAClB,IAAI,OAAO;gBACT,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;gBAChC,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;YAClC;YACA,OAAO;QACT;IACF;IAEA,+DAA+D;IAC/D,mCAAmC;IACnC,EAAE;IACF,0DAA0D;IAC1D,6DAA6D;IAC7D,MAAM;IACN,EAAE;IACF,8DAA8D;IAC9D,qDAAqD;IACrD,EAAE;IACF,wDAAwD;IACxD,iCAAiC;IACjC,SAAS,UAAW,EAAE;QACpB,IAAI,CAAC,IACH,OAAO;QAET,IAAI,GAAG,IAAI,KAAK,UACd,OAAO;QAET,IAAI,UAAU,CAAC,QAAQ,MAAM,IAAI,QAAQ,MAAM,OAAO;QACtD,IAAI,SAAS;YACX,IAAI,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,SACtC,OAAO;QACX;QAEA,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/graceful-fs/legacy-streams.js"], "sourcesContent": ["var Stream = require('stream').Stream\n\nmodule.exports = legacy\n\nfunction legacy (fs) {\n  return {\n    ReadStream: ReadStream,\n    WriteStream: WriteStream\n  }\n\n  function ReadStream (path, options) {\n    if (!(this instanceof ReadStream)) return new ReadStream(path, options);\n\n    Stream.call(this);\n\n    var self = this;\n\n    this.path = path;\n    this.fd = null;\n    this.readable = true;\n    this.paused = false;\n\n    this.flags = 'r';\n    this.mode = 438; /*=0666*/\n    this.bufferSize = 64 * 1024;\n\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n\n    if (this.encoding) this.setEncoding(this.encoding);\n\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.end === undefined) {\n        this.end = Infinity;\n      } else if ('number' !== typeof this.end) {\n        throw TypeError('end must be a Number');\n      }\n\n      if (this.start > this.end) {\n        throw new Error('start must be <= end');\n      }\n\n      this.pos = this.start;\n    }\n\n    if (this.fd !== null) {\n      process.nextTick(function() {\n        self._read();\n      });\n      return;\n    }\n\n    fs.open(this.path, this.flags, this.mode, function (err, fd) {\n      if (err) {\n        self.emit('error', err);\n        self.readable = false;\n        return;\n      }\n\n      self.fd = fd;\n      self.emit('open', fd);\n      self._read();\n    })\n  }\n\n  function WriteStream (path, options) {\n    if (!(this instanceof WriteStream)) return new WriteStream(path, options);\n\n    Stream.call(this);\n\n    this.path = path;\n    this.fd = null;\n    this.writable = true;\n\n    this.flags = 'w';\n    this.encoding = 'binary';\n    this.mode = 438; /*=0666*/\n    this.bytesWritten = 0;\n\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.start < 0) {\n        throw new Error('start must be >= zero');\n      }\n\n      this.pos = this.start;\n    }\n\n    this.busy = false;\n    this._queue = [];\n\n    if (this.fd === null) {\n      this._open = fs.open;\n      this._queue.push([this._open, this.path, this.flags, this.mode, undefined]);\n      this.flush();\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,IAAI,SAAS,uEAAkB,MAAM;AAErC,OAAO,OAAO,GAAG;AAEjB,SAAS,OAAQ,EAAE;IACjB,OAAO;QACL,YAAY;QACZ,aAAa;IACf;;IAEA,SAAS,WAAY,IAAI,EAAE,OAAO;QAChC,IAAI,CAAC,CAAC,IAAI,YAAY,UAAU,GAAG,OAAO,IAAI,WAAW,MAAM;QAE/D,OAAO,IAAI,CAAC,IAAI;QAEhB,IAAI,OAAO,IAAI;QAEf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO;QACxB,IAAI,CAAC,UAAU,GAAG,KAAK;QAEvB,UAAU,WAAW,CAAC;QAEtB,0BAA0B;QAC1B,IAAI,OAAO,OAAO,IAAI,CAAC;QACvB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,QAAQ,QAAQ,QAAS;YACjE,IAAI,MAAM,IAAI,CAAC,MAAM;YACrB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;QAC1B;QAEA,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ;QAEjD,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW;YAC5B,IAAI,aAAa,OAAO,IAAI,CAAC,KAAK,EAAE;gBAClC,MAAM,UAAU;YAClB;YACA,IAAI,IAAI,CAAC,GAAG,KAAK,WAAW;gBAC1B,IAAI,CAAC,GAAG,GAAG;YACb,OAAO,IAAI,aAAa,OAAO,IAAI,CAAC,GAAG,EAAE;gBACvC,MAAM,UAAU;YAClB;YAEA,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE;gBACzB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK;QACvB;QAEA,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM;YACpB,QAAQ,QAAQ,CAAC;gBACf,KAAK,KAAK;YACZ;YACA;QACF;QAEA,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,SAAU,GAAG,EAAE,EAAE;YACzD,IAAI,KAAK;gBACP,KAAK,IAAI,CAAC,SAAS;gBACnB,KAAK,QAAQ,GAAG;gBAChB;YACF;YAEA,KAAK,EAAE,GAAG;YACV,KAAK,IAAI,CAAC,QAAQ;YAClB,KAAK,KAAK;QACZ;IACF;IAEA,SAAS,YAAa,IAAI,EAAE,OAAO;QACjC,IAAI,CAAC,CAAC,IAAI,YAAY,WAAW,GAAG,OAAO,IAAI,YAAY,MAAM;QAEjE,OAAO,IAAI,CAAC,IAAI;QAEhB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,QAAQ,GAAG;QAEhB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO;QACxB,IAAI,CAAC,YAAY,GAAG;QAEpB,UAAU,WAAW,CAAC;QAEtB,0BAA0B;QAC1B,IAAI,OAAO,OAAO,IAAI,CAAC;QACvB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,QAAQ,QAAQ,QAAS;YACjE,IAAI,MAAM,IAAI,CAAC,MAAM;YACrB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;QAC1B;QAEA,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW;YAC5B,IAAI,aAAa,OAAO,IAAI,CAAC,KAAK,EAAE;gBAClC,MAAM,UAAU;YAClB;YACA,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG;gBAClB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK;QACvB;QAEA,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG,EAAE;QAEhB,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM;YACpB,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBAAC,IAAI,CAAC,KAAK;gBAAE,IAAI,CAAC,IAAI;gBAAE,IAAI,CAAC,KAAK;gBAAE,IAAI,CAAC,IAAI;gBAAE;aAAU;YAC1E,IAAI,CAAC,KAAK;QACZ;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/graceful-fs/clone.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = clone\n\nvar getPrototypeOf = Object.getPrototypeOf || function (obj) {\n  return obj.__proto__\n}\n\nfunction clone (obj) {\n  if (obj === null || typeof obj !== 'object')\n    return obj\n\n  if (obj instanceof Object)\n    var copy = { __proto__: getPrototypeOf(obj) }\n  else\n    var copy = Object.create(null)\n\n  Object.getOwnPropertyNames(obj).forEach(function (key) {\n    Object.defineProperty(copy, key, Object.getOwnPropertyDescriptor(obj, key))\n  })\n\n  return copy\n}\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG;AAEjB,IAAI,iBAAiB,OAAO,cAAc,IAAI,SAAU,GAAG;IACzD,OAAO,IAAI,SAAS;AACtB;AAEA,SAAS,MAAO,GAAG;IACjB,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UACjC,OAAO;IAET,IAAI,eAAe,QACjB,IAAI,OAAO;QAAE,WAAW,eAAe;IAAK;SAE5C,IAAI,OAAO,OAAO,MAAM,CAAC;IAE3B,OAAO,mBAAmB,CAAC,KAAK,OAAO,CAAC,SAAU,GAAG;QACnD,OAAO,cAAc,CAAC,MAAM,KAAK,OAAO,wBAAwB,CAAC,KAAK;IACxE;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/graceful-fs/graceful-fs.js"], "sourcesContent": ["var fs = require('fs')\nvar polyfills = require('./polyfills.js')\nvar legacy = require('./legacy-streams.js')\nvar clone = require('./clone.js')\n\nvar util = require('util')\n\n/* istanbul ignore next - node 0.x polyfill */\nvar gracefulQueue\nvar previousSymbol\n\n/* istanbul ignore else - node 0.x polyfill */\nif (typeof Symbol === 'function' && typeof Symbol.for === 'function') {\n  gracefulQueue = Symbol.for('graceful-fs.queue')\n  // This is used in testing by future versions\n  previousSymbol = Symbol.for('graceful-fs.previous')\n} else {\n  gracefulQueue = '___graceful-fs.queue'\n  previousSymbol = '___graceful-fs.previous'\n}\n\nfunction noop () {}\n\nfunction publishQueue(context, queue) {\n  Object.defineProperty(context, gracefulQueue, {\n    get: function() {\n      return queue\n    }\n  })\n}\n\nvar debug = noop\nif (util.debuglog)\n  debug = util.debuglog('gfs4')\nelse if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || ''))\n  debug = function() {\n    var m = util.format.apply(util, arguments)\n    m = 'GFS4: ' + m.split(/\\n/).join('\\nGFS4: ')\n    console.error(m)\n  }\n\n// Once time initialization\nif (!fs[gracefulQueue]) {\n  // This queue can be shared by multiple loaded instances\n  var queue = global[gracefulQueue] || []\n  publishQueue(fs, queue)\n\n  // Patch fs.close/closeSync to shared queue version, because we need\n  // to retry() whenever a close happens *anywhere* in the program.\n  // This is essential when multiple graceful-fs instances are\n  // in play at the same time.\n  fs.close = (function (fs$close) {\n    function close (fd, cb) {\n      return fs$close.call(fs, fd, function (err) {\n        // This function uses the graceful-fs shared queue\n        if (!err) {\n          resetQueue()\n        }\n\n        if (typeof cb === 'function')\n          cb.apply(this, arguments)\n      })\n    }\n\n    Object.defineProperty(close, previousSymbol, {\n      value: fs$close\n    })\n    return close\n  })(fs.close)\n\n  fs.closeSync = (function (fs$closeSync) {\n    function closeSync (fd) {\n      // This function uses the graceful-fs shared queue\n      fs$closeSync.apply(fs, arguments)\n      resetQueue()\n    }\n\n    Object.defineProperty(closeSync, previousSymbol, {\n      value: fs$closeSync\n    })\n    return closeSync\n  })(fs.closeSync)\n\n  if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || '')) {\n    process.on('exit', function() {\n      debug(fs[gracefulQueue])\n      require('assert').equal(fs[gracefulQueue].length, 0)\n    })\n  }\n}\n\nif (!global[gracefulQueue]) {\n  publishQueue(global, fs[gracefulQueue]);\n}\n\nmodule.exports = patch(clone(fs))\nif (process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH && !fs.__patched) {\n    module.exports = patch(fs)\n    fs.__patched = true;\n}\n\nfunction patch (fs) {\n  // Everything that references the open() function needs to be in here\n  polyfills(fs)\n  fs.gracefulify = patch\n\n  fs.createReadStream = createReadStream\n  fs.createWriteStream = createWriteStream\n  var fs$readFile = fs.readFile\n  fs.readFile = readFile\n  function readFile (path, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$readFile(path, options, cb)\n\n    function go$readFile (path, options, cb, startTime) {\n      return fs$readFile(path, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$readFile, [path, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$writeFile = fs.writeFile\n  fs.writeFile = writeFile\n  function writeFile (path, data, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$writeFile(path, data, options, cb)\n\n    function go$writeFile (path, data, options, cb, startTime) {\n      return fs$writeFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$writeFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$appendFile = fs.appendFile\n  if (fs$appendFile)\n    fs.appendFile = appendFile\n  function appendFile (path, data, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$appendFile(path, data, options, cb)\n\n    function go$appendFile (path, data, options, cb, startTime) {\n      return fs$appendFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$appendFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$copyFile = fs.copyFile\n  if (fs$copyFile)\n    fs.copyFile = copyFile\n  function copyFile (src, dest, flags, cb) {\n    if (typeof flags === 'function') {\n      cb = flags\n      flags = 0\n    }\n    return go$copyFile(src, dest, flags, cb)\n\n    function go$copyFile (src, dest, flags, cb, startTime) {\n      return fs$copyFile(src, dest, flags, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$copyFile, [src, dest, flags, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$readdir = fs.readdir\n  fs.readdir = readdir\n  var noReaddirOptionVersions = /^v[0-5]\\./\n  function readdir (path, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    var go$readdir = noReaddirOptionVersions.test(process.version)\n      ? function go$readdir (path, options, cb, startTime) {\n        return fs$readdir(path, fs$readdirCallback(\n          path, options, cb, startTime\n        ))\n      }\n      : function go$readdir (path, options, cb, startTime) {\n        return fs$readdir(path, options, fs$readdirCallback(\n          path, options, cb, startTime\n        ))\n      }\n\n    return go$readdir(path, options, cb)\n\n    function fs$readdirCallback (path, options, cb, startTime) {\n      return function (err, files) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([\n            go$readdir,\n            [path, options, cb],\n            err,\n            startTime || Date.now(),\n            Date.now()\n          ])\n        else {\n          if (files && files.sort)\n            files.sort()\n\n          if (typeof cb === 'function')\n            cb.call(this, err, files)\n        }\n      }\n    }\n  }\n\n  if (process.version.substr(0, 4) === 'v0.8') {\n    var legStreams = legacy(fs)\n    ReadStream = legStreams.ReadStream\n    WriteStream = legStreams.WriteStream\n  }\n\n  var fs$ReadStream = fs.ReadStream\n  if (fs$ReadStream) {\n    ReadStream.prototype = Object.create(fs$ReadStream.prototype)\n    ReadStream.prototype.open = ReadStream$open\n  }\n\n  var fs$WriteStream = fs.WriteStream\n  if (fs$WriteStream) {\n    WriteStream.prototype = Object.create(fs$WriteStream.prototype)\n    WriteStream.prototype.open = WriteStream$open\n  }\n\n  Object.defineProperty(fs, 'ReadStream', {\n    get: function () {\n      return ReadStream\n    },\n    set: function (val) {\n      ReadStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n  Object.defineProperty(fs, 'WriteStream', {\n    get: function () {\n      return WriteStream\n    },\n    set: function (val) {\n      WriteStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n\n  // legacy names\n  var FileReadStream = ReadStream\n  Object.defineProperty(fs, 'FileReadStream', {\n    get: function () {\n      return FileReadStream\n    },\n    set: function (val) {\n      FileReadStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n  var FileWriteStream = WriteStream\n  Object.defineProperty(fs, 'FileWriteStream', {\n    get: function () {\n      return FileWriteStream\n    },\n    set: function (val) {\n      FileWriteStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n\n  function ReadStream (path, options) {\n    if (this instanceof ReadStream)\n      return fs$ReadStream.apply(this, arguments), this\n    else\n      return ReadStream.apply(Object.create(ReadStream.prototype), arguments)\n  }\n\n  function ReadStream$open () {\n    var that = this\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        if (that.autoClose)\n          that.destroy()\n\n        that.emit('error', err)\n      } else {\n        that.fd = fd\n        that.emit('open', fd)\n        that.read()\n      }\n    })\n  }\n\n  function WriteStream (path, options) {\n    if (this instanceof WriteStream)\n      return fs$WriteStream.apply(this, arguments), this\n    else\n      return WriteStream.apply(Object.create(WriteStream.prototype), arguments)\n  }\n\n  function WriteStream$open () {\n    var that = this\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        that.destroy()\n        that.emit('error', err)\n      } else {\n        that.fd = fd\n        that.emit('open', fd)\n      }\n    })\n  }\n\n  function createReadStream (path, options) {\n    return new fs.ReadStream(path, options)\n  }\n\n  function createWriteStream (path, options) {\n    return new fs.WriteStream(path, options)\n  }\n\n  var fs$open = fs.open\n  fs.open = open\n  function open (path, flags, mode, cb) {\n    if (typeof mode === 'function')\n      cb = mode, mode = null\n\n    return go$open(path, flags, mode, cb)\n\n    function go$open (path, flags, mode, cb, startTime) {\n      return fs$open(path, flags, mode, function (err, fd) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$open, [path, flags, mode, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  return fs\n}\n\nfunction enqueue (elem) {\n  debug('ENQUEUE', elem[0].name, elem[1])\n  fs[gracefulQueue].push(elem)\n  retry()\n}\n\n// keep track of the timeout between retry() calls\nvar retryTimer\n\n// reset the startTime and lastTime to now\n// this resets the start of the 60 second overall timeout as well as the\n// delay between attempts so that we'll retry these jobs sooner\nfunction resetQueue () {\n  var now = Date.now()\n  for (var i = 0; i < fs[gracefulQueue].length; ++i) {\n    // entries that are only a length of 2 are from an older version, don't\n    // bother modifying those since they'll be retried anyway.\n    if (fs[gracefulQueue][i].length > 2) {\n      fs[gracefulQueue][i][3] = now // startTime\n      fs[gracefulQueue][i][4] = now // lastTime\n    }\n  }\n  // call retry to make sure we're actively processing the queue\n  retry()\n}\n\nfunction retry () {\n  // clear the timer and remove it to help prevent unintended concurrency\n  clearTimeout(retryTimer)\n  retryTimer = undefined\n\n  if (fs[gracefulQueue].length === 0)\n    return\n\n  var elem = fs[gracefulQueue].shift()\n  var fn = elem[0]\n  var args = elem[1]\n  // these items may be unset if they were added by an older graceful-fs\n  var err = elem[2]\n  var startTime = elem[3]\n  var lastTime = elem[4]\n\n  // if we don't have a startTime we have no way of knowing if we've waited\n  // long enough, so go ahead and retry this item now\n  if (startTime === undefined) {\n    debug('RETRY', fn.name, args)\n    fn.apply(null, args)\n  } else if (Date.now() - startTime >= 60000) {\n    // it's been more than 60 seconds total, bail now\n    debug('TIMEOUT', fn.name, args)\n    var cb = args.pop()\n    if (typeof cb === 'function')\n      cb.call(null, err)\n  } else {\n    // the amount of time between the last attempt and right now\n    var sinceAttempt = Date.now() - lastTime\n    // the amount of time between when we first tried, and when we last tried\n    // rounded up to at least 1\n    var sinceStart = Math.max(lastTime - startTime, 1)\n    // backoff. wait longer than the total time we've been retrying, but only\n    // up to a maximum of 100ms\n    var desiredDelay = Math.min(sinceStart * 1.2, 100)\n    // it's been long enough since the last retry, do it again\n    if (sinceAttempt >= desiredDelay) {\n      debug('RETRY', fn.name, args)\n      fn.apply(null, args.concat([startTime]))\n    } else {\n      // if we can't do this job yet, push it to the end of the queue\n      // and let the next iteration check again\n      fs[gracefulQueue].push(elem)\n    }\n  }\n\n  // schedule our next run if one isn't already scheduled\n  if (retryTimer === undefined) {\n    retryTimer = setTimeout(retry, 0)\n  }\n}\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI;AAEJ,IAAI;AAEJ,4CAA4C,GAC5C,IAAI;AACJ,IAAI;AAEJ,4CAA4C,GAC5C,IAAI,OAAO,WAAW,cAAc,OAAO,OAAO,GAAG,KAAK,YAAY;IACpE,gBAAgB,OAAO,GAAG,CAAC;IAC3B,6CAA6C;IAC7C,iBAAiB,OAAO,GAAG,CAAC;AAC9B,OAAO;IACL,gBAAgB;IAChB,iBAAiB;AACnB;AAEA,SAAS,QAAS;AAElB,SAAS,aAAa,OAAO,EAAE,KAAK;IAClC,OAAO,cAAc,CAAC,SAAS,eAAe;QAC5C,KAAK;YACH,OAAO;QACT;IACF;AACF;AAEA,IAAI,QAAQ;AACZ,IAAI,KAAK,QAAQ,EACf,QAAQ,KAAK,QAAQ,CAAC;KACnB,IAAI,YAAY,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI,KAClD,QAAQ;IACN,IAAI,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,MAAM;IAChC,IAAI,WAAW,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;IAClC,QAAQ,KAAK,CAAC;AAChB;AAEF,2BAA2B;AAC3B,IAAI,CAAC,EAAE,CAAC,cAAc,EAAE;IACtB,wDAAwD;IACxD,IAAI,QAAQ,MAAM,CAAC,cAAc,IAAI,EAAE;IACvC,aAAa,IAAI;IAEjB,oEAAoE;IACpE,iEAAiE;IACjE,4DAA4D;IAC5D,4BAA4B;IAC5B,GAAG,KAAK,GAAG,AAAC,SAAU,QAAQ;QAC5B,SAAS,MAAO,EAAE,EAAE,EAAE;YACpB,OAAO,SAAS,IAAI,CAAC,IAAI,IAAI,SAAU,GAAG;gBACxC,kDAAkD;gBAClD,IAAI,CAAC,KAAK;oBACR;gBACF;gBAEA,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;YACnB;QACF;QAEA,OAAO,cAAc,CAAC,OAAO,gBAAgB;YAC3C,OAAO;QACT;QACA,OAAO;IACT,EAAG,GAAG,KAAK;IAEX,GAAG,SAAS,GAAG,AAAC,SAAU,YAAY;QACpC,SAAS,UAAW,EAAE;YACpB,kDAAkD;YAClD,aAAa,KAAK,CAAC,IAAI;YACvB;QACF;QAEA,OAAO,cAAc,CAAC,WAAW,gBAAgB;YAC/C,OAAO;QACT;QACA,OAAO;IACT,EAAG,GAAG,SAAS;IAEf,IAAI,YAAY,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI,KAAK;QAClD,QAAQ,EAAE,CAAC,QAAQ;YACjB,MAAM,EAAE,CAAC,cAAc;YACvB,uEAAkB,KAAK,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE;QACpD;IACF;AACF;AAEA,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;IAC1B,aAAa,QAAQ,EAAE,CAAC,cAAc;AACxC;AAEA,OAAO,OAAO,GAAG,MAAM,MAAM;AAC7B,IAAI,QAAQ,GAAG,CAAC,6BAA6B,IAAI,CAAC,GAAG,SAAS,EAAE;IAC5D,OAAO,OAAO,GAAG,MAAM;IACvB,GAAG,SAAS,GAAG;AACnB;AAEA,SAAS,MAAO,EAAE;IAChB,qEAAqE;IACrE,UAAU;IACV,GAAG,WAAW,GAAG;IAEjB,GAAG,gBAAgB,GAAG;IACtB,GAAG,iBAAiB,GAAG;IACvB,IAAI,cAAc,GAAG,QAAQ;IAC7B,GAAG,QAAQ,GAAG;IACd,SAAS,SAAU,IAAI,EAAE,OAAO,EAAE,EAAE;QAClC,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,OAAO,YAAY,MAAM,SAAS;;QAElC,SAAS,YAAa,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YAChD,OAAO,YAAY,MAAM,SAAS,SAAU,GAAG;gBAC7C,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAQ,GACxD,QAAQ;oBAAC;oBAAa;wBAAC;wBAAM;wBAAS;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBACjF;oBACH,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;gBACnB;YACF;QACF;IACF;IAEA,IAAI,eAAe,GAAG,SAAS;IAC/B,GAAG,SAAS,GAAG;IACf,SAAS,UAAW,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QACzC,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,OAAO,aAAa,MAAM,MAAM,SAAS;;QAEzC,SAAS,aAAc,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACvD,OAAO,aAAa,MAAM,MAAM,SAAS,SAAU,GAAG;gBACpD,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAQ,GACxD,QAAQ;oBAAC;oBAAc;wBAAC;wBAAM;wBAAM;wBAAS;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBACxF;oBACH,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;gBACnB;YACF;QACF;IACF;IAEA,IAAI,gBAAgB,GAAG,UAAU;IACjC,IAAI,eACF,GAAG,UAAU,GAAG;IAClB,SAAS,WAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QAC1C,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,OAAO,cAAc,MAAM,MAAM,SAAS;;QAE1C,SAAS,cAAe,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACxD,OAAO,cAAc,MAAM,MAAM,SAAS,SAAU,GAAG;gBACrD,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAQ,GACxD,QAAQ;oBAAC;oBAAe;wBAAC;wBAAM;wBAAM;wBAAS;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBACzF;oBACH,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;gBACnB;YACF;QACF;IACF;IAEA,IAAI,cAAc,GAAG,QAAQ;IAC7B,IAAI,aACF,GAAG,QAAQ,GAAG;IAChB,SAAS,SAAU,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QACrC,IAAI,OAAO,UAAU,YAAY;YAC/B,KAAK;YACL,QAAQ;QACV;QACA,OAAO,YAAY,KAAK,MAAM,OAAO;;QAErC,SAAS,YAAa,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,SAAS;YACnD,OAAO,YAAY,KAAK,MAAM,OAAO,SAAU,GAAG;gBAChD,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAQ,GACxD,QAAQ;oBAAC;oBAAa;wBAAC;wBAAK;wBAAM;wBAAO;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBACpF;oBACH,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;gBACnB;YACF;QACF;IACF;IAEA,IAAI,aAAa,GAAG,OAAO;IAC3B,GAAG,OAAO,GAAG;IACb,IAAI,0BAA0B;IAC9B,SAAS,QAAS,IAAI,EAAE,OAAO,EAAE,EAAE;QACjC,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,IAAI,aAAa,wBAAwB,IAAI,CAAC,QAAQ,OAAO,IACzD,SAAS,WAAY,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACjD,OAAO,WAAW,MAAM,mBACtB,MAAM,SAAS,IAAI;QAEvB,IACE,SAAS,WAAY,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACjD,OAAO,WAAW,MAAM,SAAS,mBAC/B,MAAM,SAAS,IAAI;QAEvB;QAEF,OAAO,WAAW,MAAM,SAAS;;QAEjC,SAAS,mBAAoB,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACvD,OAAO,SAAU,GAAG,EAAE,KAAK;gBACzB,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAQ,GACxD,QAAQ;oBACN;oBACA;wBAAC;wBAAM;wBAAS;qBAAG;oBACnB;oBACA,aAAa,KAAK,GAAG;oBACrB,KAAK,GAAG;iBACT;qBACE;oBACH,IAAI,SAAS,MAAM,IAAI,EACrB,MAAM,IAAI;oBAEZ,IAAI,OAAO,OAAO,YAChB,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK;gBACvB;YACF;QACF;IACF;IAEA,IAAI,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,QAAQ;QAC3C,IAAI,aAAa,OAAO;QACxB,aAAa,WAAW,UAAU;QAClC,cAAc,WAAW,WAAW;IACtC;IAEA,IAAI,gBAAgB,GAAG,UAAU;IACjC,IAAI,eAAe;QACjB,WAAW,SAAS,GAAG,OAAO,MAAM,CAAC,cAAc,SAAS;QAC5D,WAAW,SAAS,CAAC,IAAI,GAAG;IAC9B;IAEA,IAAI,iBAAiB,GAAG,WAAW;IACnC,IAAI,gBAAgB;QAClB,YAAY,SAAS,GAAG,OAAO,MAAM,CAAC,eAAe,SAAS;QAC9D,YAAY,SAAS,CAAC,IAAI,GAAG;IAC/B;IAEA,OAAO,cAAc,CAAC,IAAI,cAAc;QACtC,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,aAAa;QACf;QACA,YAAY;QACZ,cAAc;IAChB;IACA,OAAO,cAAc,CAAC,IAAI,eAAe;QACvC,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,cAAc;QAChB;QACA,YAAY;QACZ,cAAc;IAChB;IAEA,eAAe;IACf,IAAI,iBAAiB;IACrB,OAAO,cAAc,CAAC,IAAI,kBAAkB;QAC1C,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,iBAAiB;QACnB;QACA,YAAY;QACZ,cAAc;IAChB;IACA,IAAI,kBAAkB;IACtB,OAAO,cAAc,CAAC,IAAI,mBAAmB;QAC3C,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,kBAAkB;QACpB;QACA,YAAY;QACZ,cAAc;IAChB;IAEA,SAAS,WAAY,IAAI,EAAE,OAAO;QAChC,IAAI,IAAI,YAAY,YAClB,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE,YAAY,IAAI;aAEjD,OAAO,WAAW,KAAK,CAAC,OAAO,MAAM,CAAC,WAAW,SAAS,GAAG;IACjE;IAEA,SAAS;QACP,IAAI,OAAO,IAAI;QACf,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,SAAU,GAAG,EAAE,EAAE;YACtD,IAAI,KAAK;gBACP,IAAI,KAAK,SAAS,EAChB,KAAK,OAAO;gBAEd,KAAK,IAAI,CAAC,SAAS;YACrB,OAAO;gBACL,KAAK,EAAE,GAAG;gBACV,KAAK,IAAI,CAAC,QAAQ;gBAClB,KAAK,IAAI;YACX;QACF;IACF;IAEA,SAAS,YAAa,IAAI,EAAE,OAAO;QACjC,IAAI,IAAI,YAAY,aAClB,OAAO,eAAe,KAAK,CAAC,IAAI,EAAE,YAAY,IAAI;aAElD,OAAO,YAAY,KAAK,CAAC,OAAO,MAAM,CAAC,YAAY,SAAS,GAAG;IACnE;IAEA,SAAS;QACP,IAAI,OAAO,IAAI;QACf,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,SAAU,GAAG,EAAE,EAAE;YACtD,IAAI,KAAK;gBACP,KAAK,OAAO;gBACZ,KAAK,IAAI,CAAC,SAAS;YACrB,OAAO;gBACL,KAAK,EAAE,GAAG;gBACV,KAAK,IAAI,CAAC,QAAQ;YACpB;QACF;IACF;IAEA,SAAS,iBAAkB,IAAI,EAAE,OAAO;QACtC,OAAO,IAAI,GAAG,UAAU,CAAC,MAAM;IACjC;IAEA,SAAS,kBAAmB,IAAI,EAAE,OAAO;QACvC,OAAO,IAAI,GAAG,WAAW,CAAC,MAAM;IAClC;IAEA,IAAI,UAAU,GAAG,IAAI;IACrB,GAAG,IAAI,GAAG;IACV,SAAS,KAAM,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QAClC,IAAI,OAAO,SAAS,YAClB,KAAK,MAAM,OAAO;QAEpB,OAAO,QAAQ,MAAM,OAAO,MAAM;;QAElC,SAAS,QAAS,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS;YAChD,OAAO,QAAQ,MAAM,OAAO,MAAM,SAAU,GAAG,EAAE,EAAE;gBACjD,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAQ,GACxD,QAAQ;oBAAC;oBAAS;wBAAC;wBAAM;wBAAO;wBAAM;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBACjF;oBACH,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;gBACnB;YACF;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,QAAS,IAAI;IACpB,MAAM,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;IACtC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC;IACvB;AACF;AAEA,kDAAkD;AAClD,IAAI;AAEJ,0CAA0C;AAC1C,wEAAwE;AACxE,+DAA+D;AAC/D,SAAS;IACP,IAAI,MAAM,KAAK,GAAG;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,cAAc,CAAC,MAAM,EAAE,EAAE,EAAG;QACjD,uEAAuE;QACvE,0DAA0D;QAC1D,IAAI,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;YACnC,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,YAAY;;YAC1C,EAAE,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,WAAW;;QAC3C;IACF;IACA,8DAA8D;IAC9D;AACF;AAEA,SAAS;IACP,uEAAuE;IACvE,aAAa;IACb,aAAa;IAEb,IAAI,EAAE,CAAC,cAAc,CAAC,MAAM,KAAK,GAC/B;IAEF,IAAI,OAAO,EAAE,CAAC,cAAc,CAAC,KAAK;IAClC,IAAI,KAAK,IAAI,CAAC,EAAE;IAChB,IAAI,OAAO,IAAI,CAAC,EAAE;IAClB,sEAAsE;IACtE,IAAI,MAAM,IAAI,CAAC,EAAE;IACjB,IAAI,YAAY,IAAI,CAAC,EAAE;IACvB,IAAI,WAAW,IAAI,CAAC,EAAE;IAEtB,yEAAyE;IACzE,mDAAmD;IACnD,IAAI,cAAc,WAAW;QAC3B,MAAM,SAAS,GAAG,IAAI,EAAE;QACxB,GAAG,KAAK,CAAC,MAAM;IACjB,OAAO,IAAI,KAAK,GAAG,KAAK,aAAa,OAAO;QAC1C,iDAAiD;QACjD,MAAM,WAAW,GAAG,IAAI,EAAE;QAC1B,IAAI,KAAK,KAAK,GAAG;QACjB,IAAI,OAAO,OAAO,YAChB,GAAG,IAAI,CAAC,MAAM;IAClB,OAAO;QACL,4DAA4D;QAC5D,IAAI,eAAe,KAAK,GAAG,KAAK;QAChC,yEAAyE;QACzE,2BAA2B;QAC3B,IAAI,aAAa,KAAK,GAAG,CAAC,WAAW,WAAW;QAChD,yEAAyE;QACzE,2BAA2B;QAC3B,IAAI,eAAe,KAAK,GAAG,CAAC,aAAa,KAAK;QAC9C,0DAA0D;QAC1D,IAAI,gBAAgB,cAAc;YAChC,MAAM,SAAS,GAAG,IAAI,EAAE;YACxB,GAAG,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC;gBAAC;aAAU;QACxC,OAAO;YACL,+DAA+D;YAC/D,yCAAyC;YACzC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC;QACzB;IACF;IAEA,uDAAuD;IACvD,IAAI,eAAe,WAAW;QAC5B,aAAa,WAAW,OAAO;IACjC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/fs/index.js"], "sourcesContent": ["'use strict'\n// This is adapted from https://github.com/normalize/mz\n// Copyright (c) 2014-2016 Jonathan <PERSON>g <EMAIL> and Contributors\nconst u = require('universalify').fromCallback\nconst fs = require('graceful-fs')\n\nconst api = [\n  'access',\n  'appendFile',\n  'chmod',\n  'chown',\n  'close',\n  'copyFile',\n  'cp',\n  'fchmod',\n  'fchown',\n  'fdatasync',\n  'fstat',\n  'fsync',\n  'ftruncate',\n  'futimes',\n  'glob',\n  'lchmod',\n  'lchown',\n  'lutimes',\n  'link',\n  'lstat',\n  'mkdir',\n  'mkdtemp',\n  'open',\n  'opendir',\n  'readdir',\n  'readFile',\n  'readlink',\n  'realpath',\n  'rename',\n  'rm',\n  'rmdir',\n  'stat',\n  'statfs',\n  'symlink',\n  'truncate',\n  'unlink',\n  'utimes',\n  'writeFile'\n].filter(key => {\n  // Some commands are not available on some systems. Ex:\n  // fs.cp was added in Node.js v16.7.0\n  // fs.statfs was added in Node v19.6.0, v18.15.0\n  // fs.glob was added in Node.js v22.0.0\n  // fs.lchown is not available on at least some Linux\n  return typeof fs[key] === 'function'\n})\n\n// Export cloned fs:\nObject.assign(exports, fs)\n\n// Universalify async methods:\napi.forEach(method => {\n  exports[method] = u(fs[method])\n})\n\n// We differ from mz/fs in that we still ship the old, broken, fs.exists()\n// since we are a drop-in replacement for the native module\nexports.exists = function (filename, callback) {\n  if (typeof callback === 'function') {\n    return fs.exists(filename, callback)\n  }\n  return new Promise(resolve => {\n    return fs.exists(filename, resolve)\n  })\n}\n\n// fs.read(), fs.write(), fs.readv(), & fs.writev() need special treatment due to multiple callback args\n\nexports.read = function (fd, buffer, offset, length, position, callback) {\n  if (typeof callback === 'function') {\n    return fs.read(fd, buffer, offset, length, position, callback)\n  }\n  return new Promise((resolve, reject) => {\n    fs.read(fd, buffer, offset, length, position, (err, bytesRead, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesRead, buffer })\n    })\n  })\n}\n\n// Function signature can be\n// fs.write(fd, buffer[, offset[, length[, position]]], callback)\n// OR\n// fs.write(fd, string[, position[, encoding]], callback)\n// We need to handle both cases, so we use ...args\nexports.write = function (fd, buffer, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.write(fd, buffer, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.write(fd, buffer, ...args, (err, bytesWritten, buffer) => {\n      if (err) return reject(err)\n      resolve({ bytesWritten, buffer })\n    })\n  })\n}\n\n// Function signature is\n// s.readv(fd, buffers[, position], callback)\n// We need to handle the optional arg, so we use ...args\nexports.readv = function (fd, buffers, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.readv(fd, buffers, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.readv(fd, buffers, ...args, (err, bytesRead, buffers) => {\n      if (err) return reject(err)\n      resolve({ bytesRead, buffers })\n    })\n  })\n}\n\n// Function signature is\n// s.writev(fd, buffers[, position], callback)\n// We need to handle the optional arg, so we use ...args\nexports.writev = function (fd, buffers, ...args) {\n  if (typeof args[args.length - 1] === 'function') {\n    return fs.writev(fd, buffers, ...args)\n  }\n\n  return new Promise((resolve, reject) => {\n    fs.writev(fd, buffers, ...args, (err, bytesWritten, buffers) => {\n      if (err) return reject(err)\n      resolve({ bytesWritten, buffers })\n    })\n  })\n}\n\n// fs.realpath.native sometimes not available if fs is monkey-patched\nif (typeof fs.realpath.native === 'function') {\n  exports.realpath.native = u(fs.realpath.native)\n} else {\n  process.emitWarning(\n    'fs.realpath.native is not a function. Is fs being monkey-patched?',\n    'Warning', 'fs-extra-WARN0003'\n  )\n}\n"], "names": [], "mappings": "AAAA;AACA,uDAAuD;AACvD,2EAA2E;AAC3E,MAAM,IAAI,iGAAwB,YAAY;AAC9C,MAAM;AAEN,MAAM,MAAM;IACV;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD,CAAC,MAAM,CAAC,CAAA;IACP,uDAAuD;IACvD,qCAAqC;IACrC,gDAAgD;IAChD,uCAAuC;IACvC,oDAAoD;IACpD,OAAO,OAAO,EAAE,CAAC,IAAI,KAAK;AAC5B;AAEA,oBAAoB;AACpB,OAAO,MAAM,CAAC,SAAS;AAEvB,8BAA8B;AAC9B,IAAI,OAAO,CAAC,CAAA;IACV,OAAO,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC,OAAO;AAChC;AAEA,0EAA0E;AAC1E,2DAA2D;AAC3D,QAAQ,MAAM,GAAG,SAAU,QAAQ,EAAE,QAAQ;IAC3C,IAAI,OAAO,aAAa,YAAY;QAClC,OAAO,GAAG,MAAM,CAAC,UAAU;IAC7B;IACA,OAAO,IAAI,QAAQ,CAAA;QACjB,OAAO,GAAG,MAAM,CAAC,UAAU;IAC7B;AACF;AAEA,wGAAwG;AAExG,QAAQ,IAAI,GAAG,SAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ;IACrE,IAAI,OAAO,aAAa,YAAY;QAClC,OAAO,GAAG,IAAI,CAAC,IAAI,QAAQ,QAAQ,QAAQ,UAAU;IACvD;IACA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,GAAG,IAAI,CAAC,IAAI,QAAQ,QAAQ,QAAQ,UAAU,CAAC,KAAK,WAAW;YAC7D,IAAI,KAAK,OAAO,OAAO;YACvB,QAAQ;gBAAE;gBAAW;YAAO;QAC9B;IACF;AACF;AAEA,4BAA4B;AAC5B,iEAAiE;AACjE,KAAK;AACL,yDAAyD;AACzD,kDAAkD;AAClD,QAAQ,KAAK,GAAG,SAAU,EAAE,EAAE,MAAM,EAAE,GAAG,IAAI;IAC3C,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,YAAY;QAC/C,OAAO,GAAG,KAAK,CAAC,IAAI,WAAW;IACjC;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,GAAG,KAAK,CAAC,IAAI,WAAW,MAAM,CAAC,KAAK,cAAc;YAChD,IAAI,KAAK,OAAO,OAAO;YACvB,QAAQ;gBAAE;gBAAc;YAAO;QACjC;IACF;AACF;AAEA,wBAAwB;AACxB,6CAA6C;AAC7C,wDAAwD;AACxD,QAAQ,KAAK,GAAG,SAAU,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI;IAC5C,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,YAAY;QAC/C,OAAO,GAAG,KAAK,CAAC,IAAI,YAAY;IAClC;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,GAAG,KAAK,CAAC,IAAI,YAAY,MAAM,CAAC,KAAK,WAAW;YAC9C,IAAI,KAAK,OAAO,OAAO;YACvB,QAAQ;gBAAE;gBAAW;YAAQ;QAC/B;IACF;AACF;AAEA,wBAAwB;AACxB,8CAA8C;AAC9C,wDAAwD;AACxD,QAAQ,MAAM,GAAG,SAAU,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI;IAC7C,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,KAAK,YAAY;QAC/C,OAAO,GAAG,MAAM,CAAC,IAAI,YAAY;IACnC;IAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,GAAG,MAAM,CAAC,IAAI,YAAY,MAAM,CAAC,KAAK,cAAc;YAClD,IAAI,KAAK,OAAO,OAAO;YACvB,QAAQ;gBAAE;gBAAc;YAAQ;QAClC;IACF;AACF;AAEA,qEAAqE;AACrE,IAAI,OAAO,GAAG,QAAQ,CAAC,MAAM,KAAK,YAAY;IAC5C,QAAQ,QAAQ,CAAC,MAAM,GAAG,EAAE,GAAG,QAAQ,CAAC,MAAM;AAChD,OAAO;IACL,QAAQ,WAAW,CACjB,qEACA,WAAW;AAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/mkdirs/utils.js"], "sourcesContent": ["// Adapted from https://github.com/sindresorhus/make-dir\n// Copyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n// Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n// The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n'use strict'\nconst path = require('path')\n\n// https://github.com/nodejs/node/issues/8987\n// https://github.com/libuv/libuv/pull/1088\nmodule.exports.checkPath = function checkPath (pth) {\n  if (process.platform === 'win32') {\n    const pathHasInvalidWinCharacters = /[<>:\"|?*]/.test(pth.replace(path.parse(pth).root, ''))\n\n    if (pathHasInvalidWinCharacters) {\n      const error = new Error(`Path contains invalid characters: ${pth}`)\n      error.code = 'EINVAL'\n      throw error\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,wDAAwD;AACxD,0EAA0E;AAC1E,mbAAmb;AACnb,iIAAiI;AACjI,+cAA+c;AAC/c;AACA,MAAM;AAEN,6CAA6C;AAC7C,2CAA2C;AAC3C,OAAO,OAAO,CAAC,SAAS,GAAG,SAAS,UAAW,GAAG;IAChD,wCAAkC;QAChC,MAAM,8BAA8B,YAAY,IAAI,CAAC,IAAI,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,IAAI,EAAE;QAEvF,IAAI,6BAA6B;YAC/B,MAAM,QAAQ,IAAI,MAAM,CAAC,kCAAkC,EAAE,KAAK;YAClE,MAAM,IAAI,GAAG;YACb,MAAM;QACR;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1068, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/mkdirs/make-dir.js"], "sourcesContent": ["'use strict'\nconst fs = require('../fs')\nconst { checkPath } = require('./utils')\n\nconst getMode = options => {\n  const defaults = { mode: 0o777 }\n  if (typeof options === 'number') return options\n  return ({ ...defaults, ...options }).mode\n}\n\nmodule.exports.makeDir = async (dir, options) => {\n  checkPath(dir)\n\n  return fs.mkdir(dir, {\n    mode: getMode(options),\n    recursive: true\n  })\n}\n\nmodule.exports.makeDirSync = (dir, options) => {\n  checkPath(dir)\n\n  return fs.mkdirSync(dir, {\n    mode: getMode(options),\n    recursive: true\n  })\n}\n"], "names": [], "mappings": "AAAA;AACA,MAAM;AACN,MAAM,EAAE,SAAS,EAAE;AAEnB,MAAM,UAAU,CAAA;IACd,MAAM,WAAW;QAAE,MAAM;IAAM;IAC/B,IAAI,OAAO,YAAY,UAAU,OAAO;IACxC,OAAO,CAAC;QAAE,GAAG,QAAQ;QAAE,GAAG,OAAO;IAAC,CAAC,EAAE,IAAI;AAC3C;AAEA,OAAO,OAAO,CAAC,OAAO,GAAG,OAAO,KAAK;IACnC,UAAU;IAEV,OAAO,GAAG,KAAK,CAAC,KAAK;QACnB,MAAM,QAAQ;QACd,WAAW;IACb;AACF;AAEA,OAAO,OAAO,CAAC,WAAW,GAAG,CAAC,KAAK;IACjC,UAAU;IAEV,OAAO,GAAG,SAAS,CAAC,KAAK;QACvB,MAAM,QAAQ;QACd,WAAW;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/mkdirs/index.js"], "sourcesContent": ["'use strict'\nconst u = require('universalify').fromPromise\nconst { makeDir: _makeDir, makeDirSync } = require('./make-dir')\nconst makeDir = u(_makeDir)\n\nmodule.exports = {\n  mkdirs: makeDir,\n  mkdirsSync: makeDirSync,\n  // alias\n  mkdirp: makeDir,\n  mkdirpSync: makeDirSync,\n  ensureDir: makeDir,\n  ensureDirSync: makeDirSync\n}\n"], "names": [], "mappings": "AAAA;AACA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM,EAAE,SAAS,QAAQ,EAAE,WAAW,EAAE;AACxC,MAAM,UAAU,EAAE;AAElB,OAAO,OAAO,GAAG;IACf,QAAQ;IACR,YAAY;IACZ,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,WAAW;IACX,eAAe;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/path-exists/index.js"], "sourcesContent": ["'use strict'\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\n\nfunction pathExists (path) {\n  return fs.access(path).then(() => true).catch(() => false)\n}\n\nmodule.exports = {\n  pathExists: u(pathExists),\n  pathExistsSync: fs.existsSync\n}\n"], "names": [], "mappings": "AAAA;AACA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM;AAEN,SAAS,WAAY,IAAI;IACvB,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAM,MAAM,KAAK,CAAC,IAAM;AACtD;AAEA,OAAO,OAAO,GAAG;IACf,YAAY,EAAE;IACd,gBAAgB,GAAG,UAAU;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/util/utimes.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('../fs')\nconst u = require('universalify').fromPromise\n\nasync function utimesMillis (path, atime, mtime) {\n  // if (!HAS_MILLIS_RES) return fs.utimes(path, atime, mtime, callback)\n  const fd = await fs.open(path, 'r+')\n\n  let closeErr = null\n\n  try {\n    await fs.futimes(fd, atime, mtime)\n  } finally {\n    try {\n      await fs.close(fd)\n    } catch (e) {\n      closeErr = e\n    }\n  }\n\n  if (closeErr) {\n    throw closeErr\n  }\n}\n\nfunction utimesMillisSync (path, atime, mtime) {\n  const fd = fs.openSync(path, 'r+')\n  fs.futimesSync(fd, atime, mtime)\n  return fs.closeSync(fd)\n}\n\nmodule.exports = {\n  utimesMillis: u(utimesMillis),\n  utimesMillisSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,IAAI,iGAAwB,WAAW;AAE7C,eAAe,aAAc,IAAI,EAAE,KAAK,EAAE,KAAK;IAC7C,sEAAsE;IACtE,MAAM,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM;IAE/B,IAAI,WAAW;IAEf,IAAI;QACF,MAAM,GAAG,OAAO,CAAC,IAAI,OAAO;IAC9B,SAAU;QACR,IAAI;YACF,MAAM,GAAG,KAAK,CAAC;QACjB,EAAE,OAAO,GAAG;YACV,WAAW;QACb;IACF;IAEA,IAAI,UAAU;QACZ,MAAM;IACR;AACF;AAEA,SAAS,iBAAkB,IAAI,EAAE,KAAK,EAAE,KAAK;IAC3C,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;IAC7B,GAAG,WAAW,CAAC,IAAI,OAAO;IAC1B,OAAO,GAAG,SAAS,CAAC;AACtB;AAEA,OAAO,OAAO,GAAG;IACf,cAAc,EAAE;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/util/stat.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('../fs')\nconst path = require('path')\nconst u = require('universalify').fromPromise\n\nfunction getStats (src, dest, opts) {\n  const statFunc = opts.dereference\n    ? (file) => fs.stat(file, { bigint: true })\n    : (file) => fs.lstat(file, { bigint: true })\n  return Promise.all([\n    statFunc(src),\n    statFunc(dest).catch(err => {\n      if (err.code === 'ENOENT') return null\n      throw err\n    })\n  ]).then(([srcStat, destStat]) => ({ srcStat, destStat }))\n}\n\nfunction getStatsSync (src, dest, opts) {\n  let destStat\n  const statFunc = opts.dereference\n    ? (file) => fs.statSync(file, { bigint: true })\n    : (file) => fs.lstatSync(file, { bigint: true })\n  const srcStat = statFunc(src)\n  try {\n    destStat = statFunc(dest)\n  } catch (err) {\n    if (err.code === 'ENOENT') return { srcStat, destStat: null }\n    throw err\n  }\n  return { srcStat, destStat }\n}\n\nasync function checkPaths (src, dest, funcName, opts) {\n  const { srcStat, destStat } = await getStats(src, dest, opts)\n  if (destStat) {\n    if (areIdentical(srcStat, destStat)) {\n      const srcBaseName = path.basename(src)\n      const destBaseName = path.basename(dest)\n      if (funcName === 'move' &&\n        srcBaseName !== destBaseName &&\n        srcBaseName.toLowerCase() === destBaseName.toLowerCase()) {\n        return { srcStat, destStat, isChangingCase: true }\n      }\n      throw new Error('Source and destination must not be the same.')\n    }\n    if (srcStat.isDirectory() && !destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`)\n    }\n    if (!srcStat.isDirectory() && destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite directory '${dest}' with non-directory '${src}'.`)\n    }\n  }\n\n  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n\n  return { srcStat, destStat }\n}\n\nfunction checkPathsSync (src, dest, funcName, opts) {\n  const { srcStat, destStat } = getStatsSync(src, dest, opts)\n\n  if (destStat) {\n    if (areIdentical(srcStat, destStat)) {\n      const srcBaseName = path.basename(src)\n      const destBaseName = path.basename(dest)\n      if (funcName === 'move' &&\n        srcBaseName !== destBaseName &&\n        srcBaseName.toLowerCase() === destBaseName.toLowerCase()) {\n        return { srcStat, destStat, isChangingCase: true }\n      }\n      throw new Error('Source and destination must not be the same.')\n    }\n    if (srcStat.isDirectory() && !destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite non-directory '${dest}' with directory '${src}'.`)\n    }\n    if (!srcStat.isDirectory() && destStat.isDirectory()) {\n      throw new Error(`Cannot overwrite directory '${dest}' with non-directory '${src}'.`)\n    }\n  }\n\n  if (srcStat.isDirectory() && isSrcSubdir(src, dest)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return { srcStat, destStat }\n}\n\n// recursively check if dest parent is a subdirectory of src.\n// It works for all file types including symlinks since it\n// checks the src and dest inodes. It starts from the deepest\n// parent and stops once it reaches the src parent or the root path.\nasync function checkParentPaths (src, srcStat, dest, funcName) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return\n\n  let destStat\n  try {\n    destStat = await fs.stat(destParent, { bigint: true })\n  } catch (err) {\n    if (err.code === 'ENOENT') return\n    throw err\n  }\n\n  if (areIdentical(srcStat, destStat)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n\n  return checkParentPaths(src, srcStat, destParent, funcName)\n}\n\nfunction checkParentPathsSync (src, srcStat, dest, funcName) {\n  const srcParent = path.resolve(path.dirname(src))\n  const destParent = path.resolve(path.dirname(dest))\n  if (destParent === srcParent || destParent === path.parse(destParent).root) return\n  let destStat\n  try {\n    destStat = fs.statSync(destParent, { bigint: true })\n  } catch (err) {\n    if (err.code === 'ENOENT') return\n    throw err\n  }\n  if (areIdentical(srcStat, destStat)) {\n    throw new Error(errMsg(src, dest, funcName))\n  }\n  return checkParentPathsSync(src, srcStat, destParent, funcName)\n}\n\nfunction areIdentical (srcStat, destStat) {\n  return destStat.ino && destStat.dev && destStat.ino === srcStat.ino && destStat.dev === srcStat.dev\n}\n\n// return true if dest is a subdir of src, otherwise false.\n// It only checks the path strings.\nfunction isSrcSubdir (src, dest) {\n  const srcArr = path.resolve(src).split(path.sep).filter(i => i)\n  const destArr = path.resolve(dest).split(path.sep).filter(i => i)\n  return srcArr.every((cur, i) => destArr[i] === cur)\n}\n\nfunction errMsg (src, dest, funcName) {\n  return `Cannot ${funcName} '${src}' to a subdirectory of itself, '${dest}'.`\n}\n\nmodule.exports = {\n  // checkPaths\n  checkPaths: u(checkPaths),\n  checkPathsSync,\n  // checkParent\n  checkParentPaths: u(checkParentPaths),\n  checkParentPathsSync,\n  // Misc\n  isSrcSubdir,\n  areIdentical\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,IAAI,iGAAwB,WAAW;AAE7C,SAAS,SAAU,GAAG,EAAE,IAAI,EAAE,IAAI;IAChC,MAAM,WAAW,KAAK,WAAW,GAC7B,CAAC,OAAS,GAAG,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAK,KACvC,CAAC,OAAS,GAAG,KAAK,CAAC,MAAM;YAAE,QAAQ;QAAK;IAC5C,OAAO,QAAQ,GAAG,CAAC;QACjB,SAAS;QACT,SAAS,MAAM,KAAK,CAAC,CAAA;YACnB,IAAI,IAAI,IAAI,KAAK,UAAU,OAAO;YAClC,MAAM;QACR;KACD,EAAE,IAAI,CAAC,CAAC,CAAC,SAAS,SAAS,GAAK,CAAC;YAAE;YAAS;QAAS,CAAC;AACzD;AAEA,SAAS,aAAc,GAAG,EAAE,IAAI,EAAE,IAAI;IACpC,IAAI;IACJ,MAAM,WAAW,KAAK,WAAW,GAC7B,CAAC,OAAS,GAAG,QAAQ,CAAC,MAAM;YAAE,QAAQ;QAAK,KAC3C,CAAC,OAAS,GAAG,SAAS,CAAC,MAAM;YAAE,QAAQ;QAAK;IAChD,MAAM,UAAU,SAAS;IACzB,IAAI;QACF,WAAW,SAAS;IACtB,EAAE,OAAO,KAAK;QACZ,IAAI,IAAI,IAAI,KAAK,UAAU,OAAO;YAAE;YAAS,UAAU;QAAK;QAC5D,MAAM;IACR;IACA,OAAO;QAAE;QAAS;IAAS;AAC7B;AAEA,eAAe,WAAY,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAClD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,KAAK,MAAM;IACxD,IAAI,UAAU;QACZ,IAAI,aAAa,SAAS,WAAW;YACnC,MAAM,cAAc,KAAK,QAAQ,CAAC;YAClC,MAAM,eAAe,KAAK,QAAQ,CAAC;YACnC,IAAI,aAAa,UACf,gBAAgB,gBAChB,YAAY,WAAW,OAAO,aAAa,WAAW,IAAI;gBAC1D,OAAO;oBAAE;oBAAS;oBAAU,gBAAgB;gBAAK;YACnD;YACA,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,QAAQ,WAAW,MAAM,CAAC,SAAS,WAAW,IAAI;YACpD,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,KAAK,kBAAkB,EAAE,IAAI,EAAE,CAAC;QACrF;QACA,IAAI,CAAC,QAAQ,WAAW,MAAM,SAAS,WAAW,IAAI;YACpD,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,KAAK,sBAAsB,EAAE,IAAI,EAAE,CAAC;QACrF;IACF;IAEA,IAAI,QAAQ,WAAW,MAAM,YAAY,KAAK,OAAO;QACnD,MAAM,IAAI,MAAM,OAAO,KAAK,MAAM;IACpC;IAEA,OAAO;QAAE;QAAS;IAAS;AAC7B;AAEA,SAAS,eAAgB,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI;IAChD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,aAAa,KAAK,MAAM;IAEtD,IAAI,UAAU;QACZ,IAAI,aAAa,SAAS,WAAW;YACnC,MAAM,cAAc,KAAK,QAAQ,CAAC;YAClC,MAAM,eAAe,KAAK,QAAQ,CAAC;YACnC,IAAI,aAAa,UACf,gBAAgB,gBAChB,YAAY,WAAW,OAAO,aAAa,WAAW,IAAI;gBAC1D,OAAO;oBAAE;oBAAS;oBAAU,gBAAgB;gBAAK;YACnD;YACA,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,QAAQ,WAAW,MAAM,CAAC,SAAS,WAAW,IAAI;YACpD,MAAM,IAAI,MAAM,CAAC,gCAAgC,EAAE,KAAK,kBAAkB,EAAE,IAAI,EAAE,CAAC;QACrF;QACA,IAAI,CAAC,QAAQ,WAAW,MAAM,SAAS,WAAW,IAAI;YACpD,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,KAAK,sBAAsB,EAAE,IAAI,EAAE,CAAC;QACrF;IACF;IAEA,IAAI,QAAQ,WAAW,MAAM,YAAY,KAAK,OAAO;QACnD,MAAM,IAAI,MAAM,OAAO,KAAK,MAAM;IACpC;IACA,OAAO;QAAE;QAAS;IAAS;AAC7B;AAEA,6DAA6D;AAC7D,0DAA0D;AAC1D,6DAA6D;AAC7D,oEAAoE;AACpE,eAAe,iBAAkB,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ;IAC3D,MAAM,YAAY,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC;IAC5C,MAAM,aAAa,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC;IAC7C,IAAI,eAAe,aAAa,eAAe,KAAK,KAAK,CAAC,YAAY,IAAI,EAAE;IAE5E,IAAI;IACJ,IAAI;QACF,WAAW,MAAM,GAAG,IAAI,CAAC,YAAY;YAAE,QAAQ;QAAK;IACtD,EAAE,OAAO,KAAK;QACZ,IAAI,IAAI,IAAI,KAAK,UAAU;QAC3B,MAAM;IACR;IAEA,IAAI,aAAa,SAAS,WAAW;QACnC,MAAM,IAAI,MAAM,OAAO,KAAK,MAAM;IACpC;IAEA,OAAO,iBAAiB,KAAK,SAAS,YAAY;AACpD;AAEA,SAAS,qBAAsB,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,QAAQ;IACzD,MAAM,YAAY,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC;IAC5C,MAAM,aAAa,KAAK,OAAO,CAAC,KAAK,OAAO,CAAC;IAC7C,IAAI,eAAe,aAAa,eAAe,KAAK,KAAK,CAAC,YAAY,IAAI,EAAE;IAC5E,IAAI;IACJ,IAAI;QACF,WAAW,GAAG,QAAQ,CAAC,YAAY;YAAE,QAAQ;QAAK;IACpD,EAAE,OAAO,KAAK;QACZ,IAAI,IAAI,IAAI,KAAK,UAAU;QAC3B,MAAM;IACR;IACA,IAAI,aAAa,SAAS,WAAW;QACnC,MAAM,IAAI,MAAM,OAAO,KAAK,MAAM;IACpC;IACA,OAAO,qBAAqB,KAAK,SAAS,YAAY;AACxD;AAEA,SAAS,aAAc,OAAO,EAAE,QAAQ;IACtC,OAAO,SAAS,GAAG,IAAI,SAAS,GAAG,IAAI,SAAS,GAAG,KAAK,QAAQ,GAAG,IAAI,SAAS,GAAG,KAAK,QAAQ,GAAG;AACrG;AAEA,2DAA2D;AAC3D,mCAAmC;AACnC,SAAS,YAAa,GAAG,EAAE,IAAI;IAC7B,MAAM,SAAS,KAAK,OAAO,CAAC,KAAK,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,CAAA,IAAK;IAC7D,MAAM,UAAU,KAAK,OAAO,CAAC,MAAM,KAAK,CAAC,KAAK,GAAG,EAAE,MAAM,CAAC,CAAA,IAAK;IAC/D,OAAO,OAAO,KAAK,CAAC,CAAC,KAAK,IAAM,OAAO,CAAC,EAAE,KAAK;AACjD;AAEA,SAAS,OAAQ,GAAG,EAAE,IAAI,EAAE,QAAQ;IAClC,OAAO,CAAC,OAAO,EAAE,SAAS,EAAE,EAAE,IAAI,gCAAgC,EAAE,KAAK,EAAE,CAAC;AAC9E;AAEA,OAAO,OAAO,GAAG;IACf,aAAa;IACb,YAAY,EAAE;IACd;IACA,cAAc;IACd,kBAAkB,EAAE;IACpB;IACA,OAAO;IACP;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/copy/copy.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('../fs')\nconst path = require('path')\nconst { mkdirs } = require('../mkdirs')\nconst { pathExists } = require('../path-exists')\nconst { utimesMillis } = require('../util/utimes')\nconst stat = require('../util/stat')\n\nasync function copy (src, dest, opts = {}) {\n  if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    process.emitWarning(\n      'Using the preserveTimestamps option in 32-bit node is not recommended;\\n\\n' +\n      '\\tsee https://github.com/jprichardson/node-fs-extra/issues/269',\n      'Warning', 'fs-extra-WARN0001'\n    )\n  }\n\n  const { srcStat, destStat } = await stat.checkPaths(src, dest, 'copy', opts)\n\n  await stat.checkParentPaths(src, srcStat, dest, 'copy')\n\n  const include = await runFilter(src, dest, opts)\n\n  if (!include) return\n\n  // check if the parent of dest exists, and create it if it doesn't exist\n  const destParent = path.dirname(dest)\n  const dirExists = await pathExists(destParent)\n  if (!dirExists) {\n    await mkdirs(destParent)\n  }\n\n  await getStatsAndPerformCopy(destStat, src, dest, opts)\n}\n\nasync function runFilter (src, dest, opts) {\n  if (!opts.filter) return true\n  return opts.filter(src, dest)\n}\n\nasync function getStatsAndPerformCopy (destStat, src, dest, opts) {\n  const statFn = opts.dereference ? fs.stat : fs.lstat\n  const srcStat = await statFn(src)\n\n  if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts)\n\n  if (\n    srcStat.isFile() ||\n    srcStat.isCharacterDevice() ||\n    srcStat.isBlockDevice()\n  ) return onFile(srcStat, destStat, src, dest, opts)\n\n  if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts)\n  if (srcStat.isSocket()) throw new Error(`Cannot copy a socket file: ${src}`)\n  if (srcStat.isFIFO()) throw new Error(`Cannot copy a FIFO pipe: ${src}`)\n  throw new Error(`Unknown file: ${src}`)\n}\n\nasync function onFile (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts)\n\n  if (opts.overwrite) {\n    await fs.unlink(dest)\n    return copyFile(srcStat, src, dest, opts)\n  }\n  if (opts.errorOnExist) {\n    throw new Error(`'${dest}' already exists`)\n  }\n}\n\nasync function copyFile (srcStat, src, dest, opts) {\n  await fs.copyFile(src, dest)\n  if (opts.preserveTimestamps) {\n    // Make sure the file is writable before setting the timestamp\n    // otherwise open fails with EPERM when invoked with 'r+'\n    // (through utimes call)\n    if (fileIsNotWritable(srcStat.mode)) {\n      await makeFileWritable(dest, srcStat.mode)\n    }\n\n    // Set timestamps and mode correspondingly\n\n    // Note that The initial srcStat.atime cannot be trusted\n    // because it is modified by the read(2) system call\n    // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n    const updatedSrcStat = await fs.stat(src)\n    await utimesMillis(dest, updatedSrcStat.atime, updatedSrcStat.mtime)\n  }\n\n  return fs.chmod(dest, srcStat.mode)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode) {\n  return fs.chmod(dest, srcMode | 0o200)\n}\n\nasync function onDir (srcStat, destStat, src, dest, opts) {\n  // the dest directory might not exist, create it\n  if (!destStat) {\n    await fs.mkdir(dest)\n  }\n\n  const promises = []\n\n  // loop through the files in the current directory to copy everything\n  for await (const item of await fs.opendir(src)) {\n    const srcItem = path.join(src, item.name)\n    const destItem = path.join(dest, item.name)\n\n    promises.push(\n      runFilter(srcItem, destItem, opts).then(include => {\n        if (include) {\n          // only copy the item if it matches the filter function\n          return stat.checkPaths(srcItem, destItem, 'copy', opts).then(({ destStat }) => {\n            // If the item is a copyable file, `getStatsAndPerformCopy` will copy it\n            // If the item is a directory, `getStatsAndPerformCopy` will call `onDir` recursively\n            return getStatsAndPerformCopy(destStat, srcItem, destItem, opts)\n          })\n        }\n      })\n    )\n  }\n\n  await Promise.all(promises)\n\n  if (!destStat) {\n    await fs.chmod(dest, srcStat.mode)\n  }\n}\n\nasync function onLink (destStat, src, dest, opts) {\n  let resolvedSrc = await fs.readlink(src)\n  if (opts.dereference) {\n    resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n  }\n  if (!destStat) {\n    return fs.symlink(resolvedSrc, dest)\n  }\n\n  let resolvedDest = null\n  try {\n    resolvedDest = await fs.readlink(dest)\n  } catch (e) {\n    // dest exists and is a regular file or directory,\n    // Windows may throw UNKNOWN error. If dest already exists,\n    // fs throws error anyway, so no need to guard against it here.\n    if (e.code === 'EINVAL' || e.code === 'UNKNOWN') return fs.symlink(resolvedSrc, dest)\n    throw e\n  }\n  if (opts.dereference) {\n    resolvedDest = path.resolve(process.cwd(), resolvedDest)\n  }\n  if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n    throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`)\n  }\n\n  // do not copy if src is a subdir of dest since unlinking\n  // dest in this case would result in removing src contents\n  // and therefore a broken symlink would be created.\n  if (stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n    throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`)\n  }\n\n  // copy the link\n  await fs.unlink(dest)\n  return fs.symlink(resolvedSrc, dest)\n}\n\nmodule.exports = copy\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,UAAU,EAAE;AACpB,MAAM,EAAE,YAAY,EAAE;AACtB,MAAM;AAEN,eAAe,KAAM,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACvC,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO;YAAE,QAAQ;QAAK;IACxB;IAEA,KAAK,OAAO,GAAG,aAAa,OAAO,CAAC,CAAC,KAAK,OAAO,GAAG,KAAK,0BAA0B;;IACnF,KAAK,SAAS,GAAG,eAAe,OAAO,CAAC,CAAC,KAAK,SAAS,GAAG,KAAK,OAAO,CAAC,kCAAkC;;IAEzG,qDAAqD;IACrD,uCAAwD;;IAMxD;IAEA,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,KAAK,UAAU,CAAC,KAAK,MAAM,QAAQ;IAEvE,MAAM,KAAK,gBAAgB,CAAC,KAAK,SAAS,MAAM;IAEhD,MAAM,UAAU,MAAM,UAAU,KAAK,MAAM;IAE3C,IAAI,CAAC,SAAS;IAEd,wEAAwE;IACxE,MAAM,aAAa,KAAK,OAAO,CAAC;IAChC,MAAM,YAAY,MAAM,WAAW;IACnC,IAAI,CAAC,WAAW;QACd,MAAM,OAAO;IACf;IAEA,MAAM,uBAAuB,UAAU,KAAK,MAAM;AACpD;AAEA,eAAe,UAAW,GAAG,EAAE,IAAI,EAAE,IAAI;IACvC,IAAI,CAAC,KAAK,MAAM,EAAE,OAAO;IACzB,OAAO,KAAK,MAAM,CAAC,KAAK;AAC1B;AAEA,eAAe,uBAAwB,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAC9D,MAAM,SAAS,KAAK,WAAW,GAAG,GAAG,IAAI,GAAG,GAAG,KAAK;IACpD,MAAM,UAAU,MAAM,OAAO;IAE7B,IAAI,QAAQ,WAAW,IAAI,OAAO,MAAM,SAAS,UAAU,KAAK,MAAM;IAEtE,IACE,QAAQ,MAAM,MACd,QAAQ,iBAAiB,MACzB,QAAQ,aAAa,IACrB,OAAO,OAAO,SAAS,UAAU,KAAK,MAAM;IAE9C,IAAI,QAAQ,cAAc,IAAI,OAAO,OAAO,UAAU,KAAK,MAAM;IACjE,IAAI,QAAQ,QAAQ,IAAI,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,KAAK;IAC3E,IAAI,QAAQ,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK;IACvE,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,KAAK;AACxC;AAEA,eAAe,OAAQ,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IACvD,IAAI,CAAC,UAAU,OAAO,SAAS,SAAS,KAAK,MAAM;IAEnD,IAAI,KAAK,SAAS,EAAE;QAClB,MAAM,GAAG,MAAM,CAAC;QAChB,OAAO,SAAS,SAAS,KAAK,MAAM;IACtC;IACA,IAAI,KAAK,YAAY,EAAE;QACrB,MAAM,IAAI,MAAM,CAAC,CAAC,EAAE,KAAK,gBAAgB,CAAC;IAC5C;AACF;AAEA,eAAe,SAAU,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAC/C,MAAM,GAAG,QAAQ,CAAC,KAAK;IACvB,IAAI,KAAK,kBAAkB,EAAE;QAC3B,8DAA8D;QAC9D,yDAAyD;QACzD,wBAAwB;QACxB,IAAI,kBAAkB,QAAQ,IAAI,GAAG;YACnC,MAAM,iBAAiB,MAAM,QAAQ,IAAI;QAC3C;QAEA,0CAA0C;QAE1C,wDAAwD;QACxD,oDAAoD;QACpD,2DAA2D;QAC3D,MAAM,iBAAiB,MAAM,GAAG,IAAI,CAAC;QACrC,MAAM,aAAa,MAAM,eAAe,KAAK,EAAE,eAAe,KAAK;IACrE;IAEA,OAAO,GAAG,KAAK,CAAC,MAAM,QAAQ,IAAI;AACpC;AAEA,SAAS,kBAAmB,OAAO;IACjC,OAAO,CAAC,UAAU,KAAK,MAAM;AAC/B;AAEA,SAAS,iBAAkB,IAAI,EAAE,OAAO;IACtC,OAAO,GAAG,KAAK,CAAC,MAAM,UAAU;AAClC;AAEA,eAAe,MAAO,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IACtD,gDAAgD;IAChD,IAAI,CAAC,UAAU;QACb,MAAM,GAAG,KAAK,CAAC;IACjB;IAEA,MAAM,WAAW,EAAE;IAEnB,qEAAqE;IACrE,WAAW,MAAM,QAAQ,CAAA,MAAM,GAAG,OAAO,CAAC,IAAG,EAAG;QAC9C,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,KAAK,IAAI;QACxC,MAAM,WAAW,KAAK,IAAI,CAAC,MAAM,KAAK,IAAI;QAE1C,SAAS,IAAI,CACX,UAAU,SAAS,UAAU,MAAM,IAAI,CAAC,CAAA;YACtC,IAAI,SAAS;gBACX,uDAAuD;gBACvD,OAAO,KAAK,UAAU,CAAC,SAAS,UAAU,QAAQ,MAAM,IAAI,CAAC,CAAC,EAAE,QAAQ,EAAE;oBACxE,wEAAwE;oBACxE,qFAAqF;oBACrF,OAAO,uBAAuB,UAAU,SAAS,UAAU;gBAC7D;YACF;QACF;IAEJ;IAEA,MAAM,QAAQ,GAAG,CAAC;IAElB,IAAI,CAAC,UAAU;QACb,MAAM,GAAG,KAAK,CAAC,MAAM,QAAQ,IAAI;IACnC;AACF;AAEA,eAAe,OAAQ,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAC9C,IAAI,cAAc,MAAM,GAAG,QAAQ,CAAC;IACpC,IAAI,KAAK,WAAW,EAAE;QACpB,cAAc,KAAK,OAAO,CAAC,QAAQ,GAAG,IAAI;IAC5C;IACA,IAAI,CAAC,UAAU;QACb,OAAO,GAAG,OAAO,CAAC,aAAa;IACjC;IAEA,IAAI,eAAe;IACnB,IAAI;QACF,eAAe,MAAM,GAAG,QAAQ,CAAC;IACnC,EAAE,OAAO,GAAG;QACV,kDAAkD;QAClD,2DAA2D;QAC3D,+DAA+D;QAC/D,IAAI,EAAE,IAAI,KAAK,YAAY,EAAE,IAAI,KAAK,WAAW,OAAO,GAAG,OAAO,CAAC,aAAa;QAChF,MAAM;IACR;IACA,IAAI,KAAK,WAAW,EAAE;QACpB,eAAe,KAAK,OAAO,CAAC,QAAQ,GAAG,IAAI;IAC7C;IACA,IAAI,KAAK,WAAW,CAAC,aAAa,eAAe;QAC/C,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,YAAY,gCAAgC,EAAE,aAAa,EAAE,CAAC;IAChG;IAEA,yDAAyD;IACzD,0DAA0D;IAC1D,mDAAmD;IACnD,IAAI,KAAK,WAAW,CAAC,cAAc,cAAc;QAC/C,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,aAAa,QAAQ,EAAE,YAAY,EAAE,CAAC;IAC7E;IAEA,gBAAgB;IAChB,MAAM,GAAG,MAAM,CAAC;IAChB,OAAO,GAAG,OAAO,CAAC,aAAa;AACjC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/copy/copy-sync.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst mkdirsSync = require('../mkdirs').mkdirsSync\nconst utimesMillisSync = require('../util/utimes').utimesMillisSync\nconst stat = require('../util/stat')\n\nfunction copySync (src, dest, opts) {\n  if (typeof opts === 'function') {\n    opts = { filter: opts }\n  }\n\n  opts = opts || {}\n  opts.clobber = 'clobber' in opts ? !!opts.clobber : true // default to true for now\n  opts.overwrite = 'overwrite' in opts ? !!opts.overwrite : opts.clobber // overwrite falls back to clobber\n\n  // Warn about using preserveTimestamps on 32-bit node\n  if (opts.preserveTimestamps && process.arch === 'ia32') {\n    process.emitWarning(\n      'Using the preserveTimestamps option in 32-bit node is not recommended;\\n\\n' +\n      '\\tsee https://github.com/jprichardson/node-fs-extra/issues/269',\n      'Warning', 'fs-extra-WARN0002'\n    )\n  }\n\n  const { srcStat, destStat } = stat.checkPathsSync(src, dest, 'copy', opts)\n  stat.checkParentPathsSync(src, srcStat, dest, 'copy')\n  if (opts.filter && !opts.filter(src, dest)) return\n  const destParent = path.dirname(dest)\n  if (!fs.existsSync(destParent)) mkdirsSync(destParent)\n  return getStats(destStat, src, dest, opts)\n}\n\nfunction getStats (destStat, src, dest, opts) {\n  const statSync = opts.dereference ? fs.statSync : fs.lstatSync\n  const srcStat = statSync(src)\n\n  if (srcStat.isDirectory()) return onDir(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isFile() ||\n           srcStat.isCharacterDevice() ||\n           srcStat.isBlockDevice()) return onFile(srcStat, destStat, src, dest, opts)\n  else if (srcStat.isSymbolicLink()) return onLink(destStat, src, dest, opts)\n  else if (srcStat.isSocket()) throw new Error(`Cannot copy a socket file: ${src}`)\n  else if (srcStat.isFIFO()) throw new Error(`Cannot copy a FIFO pipe: ${src}`)\n  throw new Error(`Unknown file: ${src}`)\n}\n\nfunction onFile (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return copyFile(srcStat, src, dest, opts)\n  return mayCopyFile(srcStat, src, dest, opts)\n}\n\nfunction mayCopyFile (srcStat, src, dest, opts) {\n  if (opts.overwrite) {\n    fs.unlinkSync(dest)\n    return copyFile(srcStat, src, dest, opts)\n  } else if (opts.errorOnExist) {\n    throw new Error(`'${dest}' already exists`)\n  }\n}\n\nfunction copyFile (srcStat, src, dest, opts) {\n  fs.copyFileSync(src, dest)\n  if (opts.preserveTimestamps) handleTimestamps(srcStat.mode, src, dest)\n  return setDestMode(dest, srcStat.mode)\n}\n\nfunction handleTimestamps (srcMode, src, dest) {\n  // Make sure the file is writable before setting the timestamp\n  // otherwise open fails with EPERM when invoked with 'r+'\n  // (through utimes call)\n  if (fileIsNotWritable(srcMode)) makeFileWritable(dest, srcMode)\n  return setDestTimestamps(src, dest)\n}\n\nfunction fileIsNotWritable (srcMode) {\n  return (srcMode & 0o200) === 0\n}\n\nfunction makeFileWritable (dest, srcMode) {\n  return setDestMode(dest, srcMode | 0o200)\n}\n\nfunction setDestMode (dest, srcMode) {\n  return fs.chmodSync(dest, srcMode)\n}\n\nfunction setDestTimestamps (src, dest) {\n  // The initial srcStat.atime cannot be trusted\n  // because it is modified by the read(2) system call\n  // (See https://nodejs.org/api/fs.html#fs_stat_time_values)\n  const updatedSrcStat = fs.statSync(src)\n  return utimesMillisSync(dest, updatedSrcStat.atime, updatedSrcStat.mtime)\n}\n\nfunction onDir (srcStat, destStat, src, dest, opts) {\n  if (!destStat) return mkDirAndCopy(srcStat.mode, src, dest, opts)\n  return copyDir(src, dest, opts)\n}\n\nfunction mkDirAndCopy (srcMode, src, dest, opts) {\n  fs.mkdirSync(dest)\n  copyDir(src, dest, opts)\n  return setDestMode(dest, srcMode)\n}\n\nfunction copyDir (src, dest, opts) {\n  const dir = fs.opendirSync(src)\n\n  try {\n    let dirent\n\n    while ((dirent = dir.readSync()) !== null) {\n      copyDirItem(dirent.name, src, dest, opts)\n    }\n  } finally {\n    dir.closeSync()\n  }\n}\n\nfunction copyDirItem (item, src, dest, opts) {\n  const srcItem = path.join(src, item)\n  const destItem = path.join(dest, item)\n  if (opts.filter && !opts.filter(srcItem, destItem)) return\n  const { destStat } = stat.checkPathsSync(srcItem, destItem, 'copy', opts)\n  return getStats(destStat, srcItem, destItem, opts)\n}\n\nfunction onLink (destStat, src, dest, opts) {\n  let resolvedSrc = fs.readlinkSync(src)\n  if (opts.dereference) {\n    resolvedSrc = path.resolve(process.cwd(), resolvedSrc)\n  }\n\n  if (!destStat) {\n    return fs.symlinkSync(resolvedSrc, dest)\n  } else {\n    let resolvedDest\n    try {\n      resolvedDest = fs.readlinkSync(dest)\n    } catch (err) {\n      // dest exists and is a regular file or directory,\n      // Windows may throw UNKNOWN error. If dest already exists,\n      // fs throws error anyway, so no need to guard against it here.\n      if (err.code === 'EINVAL' || err.code === 'UNKNOWN') return fs.symlinkSync(resolvedSrc, dest)\n      throw err\n    }\n    if (opts.dereference) {\n      resolvedDest = path.resolve(process.cwd(), resolvedDest)\n    }\n    if (stat.isSrcSubdir(resolvedSrc, resolvedDest)) {\n      throw new Error(`Cannot copy '${resolvedSrc}' to a subdirectory of itself, '${resolvedDest}'.`)\n    }\n\n    // prevent copy if src is a subdir of dest since unlinking\n    // dest in this case would result in removing src contents\n    // and therefore a broken symlink would be created.\n    if (stat.isSrcSubdir(resolvedDest, resolvedSrc)) {\n      throw new Error(`Cannot overwrite '${resolvedDest}' with '${resolvedSrc}'.`)\n    }\n    return copyLink(resolvedSrc, dest)\n  }\n}\n\nfunction copyLink (resolvedSrc, dest) {\n  fs.unlinkSync(dest)\n  return fs.symlinkSync(resolvedSrc, dest)\n}\n\nmodule.exports = copySync\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,aAAa,wGAAqB,UAAU;AAClD,MAAM,mBAAmB,uGAA0B,gBAAgB;AACnE,MAAM;AAEN,SAAS,SAAU,GAAG,EAAE,IAAI,EAAE,IAAI;IAChC,IAAI,OAAO,SAAS,YAAY;QAC9B,OAAO;YAAE,QAAQ;QAAK;IACxB;IAEA,OAAO,QAAQ,CAAC;IAChB,KAAK,OAAO,GAAG,aAAa,OAAO,CAAC,CAAC,KAAK,OAAO,GAAG,KAAK,0BAA0B;;IACnF,KAAK,SAAS,GAAG,eAAe,OAAO,CAAC,CAAC,KAAK,SAAS,GAAG,KAAK,OAAO,CAAC,kCAAkC;;IAEzG,qDAAqD;IACrD,uCAAwD;;IAMxD;IAEA,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,KAAK,cAAc,CAAC,KAAK,MAAM,QAAQ;IACrE,KAAK,oBAAoB,CAAC,KAAK,SAAS,MAAM;IAC9C,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK,OAAO;IAC5C,MAAM,aAAa,KAAK,OAAO,CAAC;IAChC,IAAI,CAAC,GAAG,UAAU,CAAC,aAAa,WAAW;IAC3C,OAAO,SAAS,UAAU,KAAK,MAAM;AACvC;AAEA,SAAS,SAAU,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAC1C,MAAM,WAAW,KAAK,WAAW,GAAG,GAAG,QAAQ,GAAG,GAAG,SAAS;IAC9D,MAAM,UAAU,SAAS;IAEzB,IAAI,QAAQ,WAAW,IAAI,OAAO,MAAM,SAAS,UAAU,KAAK,MAAM;SACjE,IAAI,QAAQ,MAAM,MACd,QAAQ,iBAAiB,MACzB,QAAQ,aAAa,IAAI,OAAO,OAAO,SAAS,UAAU,KAAK,MAAM;SACzE,IAAI,QAAQ,cAAc,IAAI,OAAO,OAAO,UAAU,KAAK,MAAM;SACjE,IAAI,QAAQ,QAAQ,IAAI,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,KAAK;SAC3E,IAAI,QAAQ,MAAM,IAAI,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,KAAK;IAC5E,MAAM,IAAI,MAAM,CAAC,cAAc,EAAE,KAAK;AACxC;AAEA,SAAS,OAAQ,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IACjD,IAAI,CAAC,UAAU,OAAO,SAAS,SAAS,KAAK,MAAM;IACnD,OAAO,YAAY,SAAS,KAAK,MAAM;AACzC;AAEA,SAAS,YAAa,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAC5C,IAAI,KAAK,SAAS,EAAE;QAClB,GAAG,UAAU,CAAC;QACd,OAAO,SAAS,SAAS,KAAK,MAAM;IACtC,OAAO,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM,IAAI,MAAM,CAAC,CAAC,EAAE,KAAK,gBAAgB,CAAC;IAC5C;AACF;AAEA,SAAS,SAAU,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IACzC,GAAG,YAAY,CAAC,KAAK;IACrB,IAAI,KAAK,kBAAkB,EAAE,iBAAiB,QAAQ,IAAI,EAAE,KAAK;IACjE,OAAO,YAAY,MAAM,QAAQ,IAAI;AACvC;AAEA,SAAS,iBAAkB,OAAO,EAAE,GAAG,EAAE,IAAI;IAC3C,8DAA8D;IAC9D,yDAAyD;IACzD,wBAAwB;IACxB,IAAI,kBAAkB,UAAU,iBAAiB,MAAM;IACvD,OAAO,kBAAkB,KAAK;AAChC;AAEA,SAAS,kBAAmB,OAAO;IACjC,OAAO,CAAC,UAAU,KAAK,MAAM;AAC/B;AAEA,SAAS,iBAAkB,IAAI,EAAE,OAAO;IACtC,OAAO,YAAY,MAAM,UAAU;AACrC;AAEA,SAAS,YAAa,IAAI,EAAE,OAAO;IACjC,OAAO,GAAG,SAAS,CAAC,MAAM;AAC5B;AAEA,SAAS,kBAAmB,GAAG,EAAE,IAAI;IACnC,8CAA8C;IAC9C,oDAAoD;IACpD,2DAA2D;IAC3D,MAAM,iBAAiB,GAAG,QAAQ,CAAC;IACnC,OAAO,iBAAiB,MAAM,eAAe,KAAK,EAAE,eAAe,KAAK;AAC1E;AAEA,SAAS,MAAO,OAAO,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAChD,IAAI,CAAC,UAAU,OAAO,aAAa,QAAQ,IAAI,EAAE,KAAK,MAAM;IAC5D,OAAO,QAAQ,KAAK,MAAM;AAC5B;AAEA,SAAS,aAAc,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IAC7C,GAAG,SAAS,CAAC;IACb,QAAQ,KAAK,MAAM;IACnB,OAAO,YAAY,MAAM;AAC3B;AAEA,SAAS,QAAS,GAAG,EAAE,IAAI,EAAE,IAAI;IAC/B,MAAM,MAAM,GAAG,WAAW,CAAC;IAE3B,IAAI;QACF,IAAI;QAEJ,MAAO,CAAC,SAAS,IAAI,QAAQ,EAAE,MAAM,KAAM;YACzC,YAAY,OAAO,IAAI,EAAE,KAAK,MAAM;QACtC;IACF,SAAU;QACR,IAAI,SAAS;IACf;AACF;AAEA,SAAS,YAAa,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IACzC,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK;IAC/B,MAAM,WAAW,KAAK,IAAI,CAAC,MAAM;IACjC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,SAAS,WAAW;IACpD,MAAM,EAAE,QAAQ,EAAE,GAAG,KAAK,cAAc,CAAC,SAAS,UAAU,QAAQ;IACpE,OAAO,SAAS,UAAU,SAAS,UAAU;AAC/C;AAEA,SAAS,OAAQ,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI;IACxC,IAAI,cAAc,GAAG,YAAY,CAAC;IAClC,IAAI,KAAK,WAAW,EAAE;QACpB,cAAc,KAAK,OAAO,CAAC,QAAQ,GAAG,IAAI;IAC5C;IAEA,IAAI,CAAC,UAAU;QACb,OAAO,GAAG,WAAW,CAAC,aAAa;IACrC,OAAO;QACL,IAAI;QACJ,IAAI;YACF,eAAe,GAAG,YAAY,CAAC;QACjC,EAAE,OAAO,KAAK;YACZ,kDAAkD;YAClD,2DAA2D;YAC3D,+DAA+D;YAC/D,IAAI,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,WAAW,OAAO,GAAG,WAAW,CAAC,aAAa;YACxF,MAAM;QACR;QACA,IAAI,KAAK,WAAW,EAAE;YACpB,eAAe,KAAK,OAAO,CAAC,QAAQ,GAAG,IAAI;QAC7C;QACA,IAAI,KAAK,WAAW,CAAC,aAAa,eAAe;YAC/C,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE,YAAY,gCAAgC,EAAE,aAAa,EAAE,CAAC;QAChG;QAEA,0DAA0D;QAC1D,0DAA0D;QAC1D,mDAAmD;QACnD,IAAI,KAAK,WAAW,CAAC,cAAc,cAAc;YAC/C,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,aAAa,QAAQ,EAAE,YAAY,EAAE,CAAC;QAC7E;QACA,OAAO,SAAS,aAAa;IAC/B;AACF;AAEA,SAAS,SAAU,WAAW,EAAE,IAAI;IAClC,GAAG,UAAU,CAAC;IACd,OAAO,GAAG,WAAW,CAAC,aAAa;AACrC;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/copy/index.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nmodule.exports = {\n  copy: u(require('./copy')),\n  copySync: require('./copy-sync')\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,OAAO,OAAO,GAAG;IACf,MAAM;IACN,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/remove/index.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('graceful-fs')\nconst u = require('universalify').fromCallback\n\nfunction remove (path, callback) {\n  fs.rm(path, { recursive: true, force: true }, callback)\n}\n\nfunction removeSync (path) {\n  fs.rmSync(path, { recursive: true, force: true })\n}\n\nmodule.exports = {\n  remove: u(remove),\n  removeSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,IAAI,iGAAwB,YAAY;AAE9C,SAAS,OAAQ,IAAI,EAAE,QAAQ;IAC7B,GAAG,EAAE,CAAC,MAAM;QAAE,WAAW;QAAM,OAAO;IAAK,GAAG;AAChD;AAEA,SAAS,WAAY,IAAI;IACvB,GAAG,MAAM,CAAC,MAAM;QAAE,WAAW;QAAM,OAAO;IAAK;AACjD;AAEA,OAAO,OAAO,GAAG;IACf,QAAQ,EAAE;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/empty/index.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst remove = require('../remove')\n\nconst emptyDir = u(async function emptyDir (dir) {\n  let items\n  try {\n    items = await fs.readdir(dir)\n  } catch {\n    return mkdir.mkdirs(dir)\n  }\n\n  return Promise.all(items.map(item => remove.remove(path.join(dir, item))))\n})\n\nfunction emptyDirSync (dir) {\n  let items\n  try {\n    items = fs.readdirSync(dir)\n  } catch {\n    return mkdir.mkdirsSync(dir)\n  }\n\n  items.forEach(item => {\n    item = path.join(dir, item)\n    remove.removeSync(item)\n  })\n}\n\nmodule.exports = {\n  emptyDirSync,\n  emptydirSync: emptyDirSync,\n  emptyDir,\n  emptydir: emptyDir\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AAEN,MAAM,WAAW,EAAE,eAAe,SAAU,GAAG;IAC7C,IAAI;IACJ,IAAI;QACF,QAAQ,MAAM,GAAG,OAAO,CAAC;IAC3B,EAAE,OAAM;QACN,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO,QAAQ,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,KAAK,IAAI,CAAC,KAAK;AACpE;AAEA,SAAS,aAAc,GAAG;IACxB,IAAI;IACJ,IAAI;QACF,QAAQ,GAAG,WAAW,CAAC;IACzB,EAAE,OAAM;QACN,OAAO,MAAM,UAAU,CAAC;IAC1B;IAEA,MAAM,OAAO,CAAC,CAAA;QACZ,OAAO,KAAK,IAAI,CAAC,KAAK;QACtB,OAAO,UAAU,CAAC;IACpB;AACF;AAEA,OAAO,OAAO,GAAG;IACf;IACA,cAAc;IACd;IACA,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/ensure/file.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nconst path = require('path')\nconst fs = require('../fs')\nconst mkdir = require('../mkdirs')\n\nasync function createFile (file) {\n  let stats\n  try {\n    stats = await fs.stat(file)\n  } catch { }\n  if (stats && stats.isFile()) return\n\n  const dir = path.dirname(file)\n\n  let dirStats = null\n  try {\n    dirStats = await fs.stat(dir)\n  } catch (err) {\n    // if the directory doesn't exist, make it\n    if (err.code === 'ENOENT') {\n      await mkdir.mkdirs(dir)\n      await fs.writeFile(file, '')\n      return\n    } else {\n      throw err\n    }\n  }\n\n  if (dirStats.isDirectory()) {\n    await fs.writeFile(file, '')\n  } else {\n    // parent is not a directory\n    // This is just to cause an internal ENOTDIR error to be thrown\n    await fs.readdir(dir)\n  }\n}\n\nfunction createFileSync (file) {\n  let stats\n  try {\n    stats = fs.statSync(file)\n  } catch { }\n  if (stats && stats.isFile()) return\n\n  const dir = path.dirname(file)\n  try {\n    if (!fs.statSync(dir).isDirectory()) {\n      // parent is not a directory\n      // This is just to cause an internal ENOTDIR error to be thrown\n      fs.readdirSync(dir)\n    }\n  } catch (err) {\n    // If the stat call above failed because the directory doesn't exist, create it\n    if (err && err.code === 'ENOENT') mkdir.mkdirsSync(dir)\n    else throw err\n  }\n\n  fs.writeFileSync(file, '')\n}\n\nmodule.exports = {\n  createFile: u(createFile),\n  createFileSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM;AACN,MAAM;AACN,MAAM;AAEN,eAAe,WAAY,IAAI;IAC7B,IAAI;IACJ,IAAI;QACF,QAAQ,MAAM,GAAG,IAAI,CAAC;IACxB,EAAE,OAAM,CAAE;IACV,IAAI,SAAS,MAAM,MAAM,IAAI;IAE7B,MAAM,MAAM,KAAK,OAAO,CAAC;IAEzB,IAAI,WAAW;IACf,IAAI;QACF,WAAW,MAAM,GAAG,IAAI,CAAC;IAC3B,EAAE,OAAO,KAAK;QACZ,0CAA0C;QAC1C,IAAI,IAAI,IAAI,KAAK,UAAU;YACzB,MAAM,MAAM,MAAM,CAAC;YACnB,MAAM,GAAG,SAAS,CAAC,MAAM;YACzB;QACF,OAAO;YACL,MAAM;QACR;IACF;IAEA,IAAI,SAAS,WAAW,IAAI;QAC1B,MAAM,GAAG,SAAS,CAAC,MAAM;IAC3B,OAAO;QACL,4BAA4B;QAC5B,+DAA+D;QAC/D,MAAM,GAAG,OAAO,CAAC;IACnB;AACF;AAEA,SAAS,eAAgB,IAAI;IAC3B,IAAI;IACJ,IAAI;QACF,QAAQ,GAAG,QAAQ,CAAC;IACtB,EAAE,OAAM,CAAE;IACV,IAAI,SAAS,MAAM,MAAM,IAAI;IAE7B,MAAM,MAAM,KAAK,OAAO,CAAC;IACzB,IAAI;QACF,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,WAAW,IAAI;YACnC,4BAA4B;YAC5B,+DAA+D;YAC/D,GAAG,WAAW,CAAC;QACjB;IACF,EAAE,OAAO,KAAK;QACZ,+EAA+E;QAC/E,IAAI,OAAO,IAAI,IAAI,KAAK,UAAU,MAAM,UAAU,CAAC;aAC9C,MAAM;IACb;IAEA,GAAG,aAAa,CAAC,MAAM;AACzB;AAEA,OAAO,OAAO,GAAG;IACf,YAAY,EAAE;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/ensure/link.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nconst path = require('path')\nconst fs = require('../fs')\nconst mkdir = require('../mkdirs')\nconst { pathExists } = require('../path-exists')\nconst { areIdentical } = require('../util/stat')\n\nasync function createLink (srcpath, dstpath) {\n  let dstStat\n  try {\n    dstStat = await fs.lstat(dstpath)\n  } catch {\n    // ignore error\n  }\n\n  let srcStat\n  try {\n    srcStat = await fs.lstat(srcpath)\n  } catch (err) {\n    err.message = err.message.replace('lstat', 'ensureLink')\n    throw err\n  }\n\n  if (dstStat && areIdentical(srcStat, dstStat)) return\n\n  const dir = path.dirname(dstpath)\n\n  const dirExists = await pathExists(dir)\n\n  if (!dirExists) {\n    await mkdir.mkdirs(dir)\n  }\n\n  await fs.link(srcpath, dstpath)\n}\n\nfunction createLinkSync (srcpath, dstpath) {\n  let dstStat\n  try {\n    dstStat = fs.lstatSync(dstpath)\n  } catch {}\n\n  try {\n    const srcStat = fs.lstatSync(srcpath)\n    if (dstStat && areIdentical(srcStat, dstStat)) return\n  } catch (err) {\n    err.message = err.message.replace('lstat', 'ensureLink')\n    throw err\n  }\n\n  const dir = path.dirname(dstpath)\n  const dirExists = fs.existsSync(dir)\n  if (dirExists) return fs.linkSync(srcpath, dstpath)\n  mkdir.mkdirsSync(dir)\n\n  return fs.linkSync(srcpath, dstpath)\n}\n\nmodule.exports = {\n  createLink: u(createLink),\n  createLinkSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,UAAU,EAAE;AACpB,MAAM,EAAE,YAAY,EAAE;AAEtB,eAAe,WAAY,OAAO,EAAE,OAAO;IACzC,IAAI;IACJ,IAAI;QACF,UAAU,MAAM,GAAG,KAAK,CAAC;IAC3B,EAAE,OAAM;IACN,eAAe;IACjB;IAEA,IAAI;IACJ,IAAI;QACF,UAAU,MAAM,GAAG,KAAK,CAAC;IAC3B,EAAE,OAAO,KAAK;QACZ,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;QAC3C,MAAM;IACR;IAEA,IAAI,WAAW,aAAa,SAAS,UAAU;IAE/C,MAAM,MAAM,KAAK,OAAO,CAAC;IAEzB,MAAM,YAAY,MAAM,WAAW;IAEnC,IAAI,CAAC,WAAW;QACd,MAAM,MAAM,MAAM,CAAC;IACrB;IAEA,MAAM,GAAG,IAAI,CAAC,SAAS;AACzB;AAEA,SAAS,eAAgB,OAAO,EAAE,OAAO;IACvC,IAAI;IACJ,IAAI;QACF,UAAU,GAAG,SAAS,CAAC;IACzB,EAAE,OAAM,CAAC;IAET,IAAI;QACF,MAAM,UAAU,GAAG,SAAS,CAAC;QAC7B,IAAI,WAAW,aAAa,SAAS,UAAU;IACjD,EAAE,OAAO,KAAK;QACZ,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;QAC3C,MAAM;IACR;IAEA,MAAM,MAAM,KAAK,OAAO,CAAC;IACzB,MAAM,YAAY,GAAG,UAAU,CAAC;IAChC,IAAI,WAAW,OAAO,GAAG,QAAQ,CAAC,SAAS;IAC3C,MAAM,UAAU,CAAC;IAEjB,OAAO,GAAG,QAAQ,CAAC,SAAS;AAC9B;AAEA,OAAO,OAAO,GAAG;IACf,YAAY,EAAE;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1825, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/ensure/symlink-paths.js"], "sourcesContent": ["'use strict'\n\nconst path = require('path')\nconst fs = require('../fs')\nconst { pathExists } = require('../path-exists')\n\nconst u = require('universalify').fromPromise\n\n/**\n * Function that returns two types of paths, one relative to symlink, and one\n * relative to the current working directory. Checks if path is absolute or\n * relative. If the path is relative, this function checks if the path is\n * relative to symlink or relative to current working directory. This is an\n * initiative to find a smarter `srcpath` to supply when building symlinks.\n * This allows you to determine which path to use out of one of three possible\n * types of source paths. The first is an absolute path. This is detected by\n * `path.isAbsolute()`. When an absolute path is provided, it is checked to\n * see if it exists. If it does it's used, if not an error is returned\n * (callback)/ thrown (sync). The other two options for `srcpath` are a\n * relative url. By default Node's `fs.symlink` works by creating a symlink\n * using `dstpath` and expects the `srcpath` to be relative to the newly\n * created symlink. If you provide a `srcpath` that does not exist on the file\n * system it results in a broken symlink. To minimize this, the function\n * checks to see if the 'relative to symlink' source file exists, and if it\n * does it will use it. If it does not, it checks if there's a file that\n * exists that is relative to the current working directory, if does its used.\n * This preserves the expectations of the original fs.symlink spec and adds\n * the ability to pass in `relative to current working direcotry` paths.\n */\n\nasync function symlinkPaths (srcpath, dstpath) {\n  if (path.isAbsolute(srcpath)) {\n    try {\n      await fs.lstat(srcpath)\n    } catch (err) {\n      err.message = err.message.replace('lstat', 'ensureSymlink')\n      throw err\n    }\n\n    return {\n      toCwd: srcpath,\n      toDst: srcpath\n    }\n  }\n\n  const dstdir = path.dirname(dstpath)\n  const relativeToDst = path.join(dstdir, srcpath)\n\n  const exists = await pathExists(relativeToDst)\n  if (exists) {\n    return {\n      toCwd: relativeToDst,\n      toDst: srcpath\n    }\n  }\n\n  try {\n    await fs.lstat(srcpath)\n  } catch (err) {\n    err.message = err.message.replace('lstat', 'ensureSymlink')\n    throw err\n  }\n\n  return {\n    toCwd: srcpath,\n    toDst: path.relative(dstdir, srcpath)\n  }\n}\n\nfunction symlinkPathsSync (srcpath, dstpath) {\n  if (path.isAbsolute(srcpath)) {\n    const exists = fs.existsSync(srcpath)\n    if (!exists) throw new Error('absolute srcpath does not exist')\n    return {\n      toCwd: srcpath,\n      toDst: srcpath\n    }\n  }\n\n  const dstdir = path.dirname(dstpath)\n  const relativeToDst = path.join(dstdir, srcpath)\n  const exists = fs.existsSync(relativeToDst)\n  if (exists) {\n    return {\n      toCwd: relativeToDst,\n      toDst: srcpath\n    }\n  }\n\n  const srcExists = fs.existsSync(srcpath)\n  if (!srcExists) throw new Error('relative srcpath does not exist')\n  return {\n    toCwd: srcpath,\n    toDst: path.relative(dstdir, srcpath)\n  }\n}\n\nmodule.exports = {\n  symlinkPaths: u(symlinkPaths),\n  symlinkPathsSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,UAAU,EAAE;AAEpB,MAAM,IAAI,iGAAwB,WAAW;AAE7C;;;;;;;;;;;;;;;;;;;;CAoBC,GAED,eAAe,aAAc,OAAO,EAAE,OAAO;IAC3C,IAAI,KAAK,UAAU,CAAC,UAAU;QAC5B,IAAI;YACF,MAAM,GAAG,KAAK,CAAC;QACjB,EAAE,OAAO,KAAK;YACZ,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;YAC3C,MAAM;QACR;QAEA,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,MAAM,SAAS,KAAK,OAAO,CAAC;IAC5B,MAAM,gBAAgB,KAAK,IAAI,CAAC,QAAQ;IAExC,MAAM,SAAS,MAAM,WAAW;IAChC,IAAI,QAAQ;QACV,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,IAAI;QACF,MAAM,GAAG,KAAK,CAAC;IACjB,EAAE,OAAO,KAAK;QACZ,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO,CAAC,SAAS;QAC3C,MAAM;IACR;IAEA,OAAO;QACL,OAAO;QACP,OAAO,KAAK,QAAQ,CAAC,QAAQ;IAC/B;AACF;AAEA,SAAS,iBAAkB,OAAO,EAAE,OAAO;IACzC,IAAI,KAAK,UAAU,CAAC,UAAU;QAC5B,MAAM,SAAS,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM;QAC7B,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,MAAM,SAAS,KAAK,OAAO,CAAC;IAC5B,MAAM,gBAAgB,KAAK,IAAI,CAAC,QAAQ;IACxC,MAAM,SAAS,GAAG,UAAU,CAAC;IAC7B,IAAI,QAAQ;QACV,OAAO;YACL,OAAO;YACP,OAAO;QACT;IACF;IAEA,MAAM,YAAY,GAAG,UAAU,CAAC;IAChC,IAAI,CAAC,WAAW,MAAM,IAAI,MAAM;IAChC,OAAO;QACL,OAAO;QACP,OAAO,KAAK,QAAQ,CAAC,QAAQ;IAC/B;AACF;AAEA,OAAO,OAAO,GAAG;IACf,cAAc,EAAE;IAChB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/ensure/symlink-type.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('../fs')\nconst u = require('universalify').fromPromise\n\nasync function symlinkType (srcpath, type) {\n  if (type) return type\n\n  let stats\n  try {\n    stats = await fs.lstat(srcpath)\n  } catch {\n    return 'file'\n  }\n\n  return (stats && stats.isDirectory()) ? 'dir' : 'file'\n}\n\nfunction symlinkTypeSync (srcpath, type) {\n  if (type) return type\n\n  let stats\n  try {\n    stats = fs.lstatSync(srcpath)\n  } catch {\n    return 'file'\n  }\n  return (stats && stats.isDirectory()) ? 'dir' : 'file'\n}\n\nmodule.exports = {\n  symlinkType: u(symlinkType),\n  symlinkTypeSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,IAAI,iGAAwB,WAAW;AAE7C,eAAe,YAAa,OAAO,EAAE,IAAI;IACvC,IAAI,MAAM,OAAO;IAEjB,IAAI;IACJ,IAAI;QACF,QAAQ,MAAM,GAAG,KAAK,CAAC;IACzB,EAAE,OAAM;QACN,OAAO;IACT;IAEA,OAAO,AAAC,SAAS,MAAM,WAAW,KAAM,QAAQ;AAClD;AAEA,SAAS,gBAAiB,OAAO,EAAE,IAAI;IACrC,IAAI,MAAM,OAAO;IAEjB,IAAI;IACJ,IAAI;QACF,QAAQ,GAAG,SAAS,CAAC;IACvB,EAAE,OAAM;QACN,OAAO;IACT;IACA,OAAO,AAAC,SAAS,MAAM,WAAW,KAAM,QAAQ;AAClD;AAEA,OAAO,OAAO,GAAG;IACf,aAAa,EAAE;IACf;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1949, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/ensure/symlink.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nconst path = require('path')\nconst fs = require('../fs')\n\nconst { mkdirs, mkdirsSync } = require('../mkdirs')\n\nconst { symlinkPaths, symlinkPathsSync } = require('./symlink-paths')\nconst { symlinkType, symlinkTypeSync } = require('./symlink-type')\n\nconst { pathExists } = require('../path-exists')\n\nconst { areIdentical } = require('../util/stat')\n\nasync function createSymlink (srcpath, dstpath, type) {\n  let stats\n  try {\n    stats = await fs.lstat(dstpath)\n  } catch { }\n\n  if (stats && stats.isSymbolicLink()) {\n    const [srcStat, dstStat] = await Promise.all([\n      fs.stat(srcpath),\n      fs.stat(dstpath)\n    ])\n\n    if (areIdentical(srcStat, dstStat)) return\n  }\n\n  const relative = await symlinkPaths(srcpath, dstpath)\n  srcpath = relative.toDst\n  const toType = await symlinkType(relative.toCwd, type)\n  const dir = path.dirname(dstpath)\n\n  if (!(await pathExists(dir))) {\n    await mkdirs(dir)\n  }\n\n  return fs.symlink(srcpath, dstpath, toType)\n}\n\nfunction createSymlinkSync (srcpath, dstpath, type) {\n  let stats\n  try {\n    stats = fs.lstatSync(dstpath)\n  } catch { }\n  if (stats && stats.isSymbolicLink()) {\n    const srcStat = fs.statSync(srcpath)\n    const dstStat = fs.statSync(dstpath)\n    if (areIdentical(srcStat, dstStat)) return\n  }\n\n  const relative = symlinkPathsSync(srcpath, dstpath)\n  srcpath = relative.toDst\n  type = symlinkTypeSync(relative.toCwd, type)\n  const dir = path.dirname(dstpath)\n  const exists = fs.existsSync(dir)\n  if (exists) return fs.symlinkSync(srcpath, dstpath, type)\n  mkdirsSync(dir)\n  return fs.symlinkSync(srcpath, dstpath, type)\n}\n\nmodule.exports = {\n  createSymlink: u(createSymlink),\n  createSymlinkSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM;AACN,MAAM;AAEN,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;AAE5B,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE;AACxC,MAAM,EAAE,WAAW,EAAE,eAAe,EAAE;AAEtC,MAAM,EAAE,UAAU,EAAE;AAEpB,MAAM,EAAE,YAAY,EAAE;AAEtB,eAAe,cAAe,OAAO,EAAE,OAAO,EAAE,IAAI;IAClD,IAAI;IACJ,IAAI;QACF,QAAQ,MAAM,GAAG,KAAK,CAAC;IACzB,EAAE,OAAM,CAAE;IAEV,IAAI,SAAS,MAAM,cAAc,IAAI;QACnC,MAAM,CAAC,SAAS,QAAQ,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC3C,GAAG,IAAI,CAAC;YACR,GAAG,IAAI,CAAC;SACT;QAED,IAAI,aAAa,SAAS,UAAU;IACtC;IAEA,MAAM,WAAW,MAAM,aAAa,SAAS;IAC7C,UAAU,SAAS,KAAK;IACxB,MAAM,SAAS,MAAM,YAAY,SAAS,KAAK,EAAE;IACjD,MAAM,MAAM,KAAK,OAAO,CAAC;IAEzB,IAAI,CAAE,MAAM,WAAW,MAAO;QAC5B,MAAM,OAAO;IACf;IAEA,OAAO,GAAG,OAAO,CAAC,SAAS,SAAS;AACtC;AAEA,SAAS,kBAAmB,OAAO,EAAE,OAAO,EAAE,IAAI;IAChD,IAAI;IACJ,IAAI;QACF,QAAQ,GAAG,SAAS,CAAC;IACvB,EAAE,OAAM,CAAE;IACV,IAAI,SAAS,MAAM,cAAc,IAAI;QACnC,MAAM,UAAU,GAAG,QAAQ,CAAC;QAC5B,MAAM,UAAU,GAAG,QAAQ,CAAC;QAC5B,IAAI,aAAa,SAAS,UAAU;IACtC;IAEA,MAAM,WAAW,iBAAiB,SAAS;IAC3C,UAAU,SAAS,KAAK;IACxB,OAAO,gBAAgB,SAAS,KAAK,EAAE;IACvC,MAAM,MAAM,KAAK,OAAO,CAAC;IACzB,MAAM,SAAS,GAAG,UAAU,CAAC;IAC7B,IAAI,QAAQ,OAAO,GAAG,WAAW,CAAC,SAAS,SAAS;IACpD,WAAW;IACX,OAAO,GAAG,WAAW,CAAC,SAAS,SAAS;AAC1C;AAEA,OAAO,OAAO,GAAG;IACf,eAAe,EAAE;IACjB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2007, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/ensure/index.js"], "sourcesContent": ["'use strict'\n\nconst { createFile, createFileSync } = require('./file')\nconst { createLink, createLinkSync } = require('./link')\nconst { createSymlink, createSymlinkSync } = require('./symlink')\n\nmodule.exports = {\n  // file\n  createFile,\n  createFileSync,\n  ensureFile: createFile,\n  ensureFileSync: createFileSync,\n  // link\n  createLink,\n  createLinkSync,\n  ensureLink: createLink,\n  ensureLinkSync: createLinkSync,\n  // symlink\n  createSymlink,\n  createSymlinkSync,\n  ensureSymlink: createSymlink,\n  ensureSymlinkSync: createSymlinkSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE;AACpC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE;AACpC,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE;AAE1C,OAAO,OAAO,GAAG;IACf,OAAO;IACP;IACA;IACA,YAAY;IACZ,gBAAgB;IAChB,OAAO;IACP;IACA;IACA,YAAY;IACZ,gBAAgB;IAChB,UAAU;IACV;IACA;IACA,eAAe;IACf,mBAAmB;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/json/jsonfile.js"], "sourcesContent": ["'use strict'\n\nconst jsonFile = require('jsonfile')\n\nmodule.exports = {\n  // jsonfile exports\n  readJson: jsonFile.readFile,\n  readJsonSync: jsonFile.readFileSync,\n  writeJson: jsonFile.writeFile,\n  writeJsonSync: jsonFile.writeFileSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AAEN,OAAO,OAAO,GAAG;IACf,mBAAmB;IACnB,UAAU,SAAS,QAAQ;IAC3B,cAAc,SAAS,YAAY;IACnC,WAAW,SAAS,SAAS;IAC7B,eAAe,SAAS,aAAa;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/output-file/index.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nconst fs = require('../fs')\nconst path = require('path')\nconst mkdir = require('../mkdirs')\nconst pathExists = require('../path-exists').pathExists\n\nasync function outputFile (file, data, encoding = 'utf-8') {\n  const dir = path.dirname(file)\n\n  if (!(await pathExists(dir))) {\n    await mkdir.mkdirs(dir)\n  }\n\n  return fs.writeFile(file, data, encoding)\n}\n\nfunction outputFileSync (file, ...args) {\n  const dir = path.dirname(file)\n  if (!fs.existsSync(dir)) {\n    mkdir.mkdirsSync(dir)\n  }\n\n  fs.writeFileSync(file, ...args)\n}\n\nmodule.exports = {\n  outputFile: u(outputFile),\n  outputFileSync\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,aAAa,6GAA0B,UAAU;AAEvD,eAAe,WAAY,IAAI,EAAE,IAAI,EAAE,WAAW,OAAO;IACvD,MAAM,MAAM,KAAK,OAAO,CAAC;IAEzB,IAAI,CAAE,MAAM,WAAW,MAAO;QAC5B,MAAM,MAAM,MAAM,CAAC;IACrB;IAEA,OAAO,GAAG,SAAS,CAAC,MAAM,MAAM;AAClC;AAEA,SAAS,eAAgB,IAAI,EAAE,GAAG,IAAI;IACpC,MAAM,MAAM,KAAK,OAAO,CAAC;IACzB,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM;QACvB,MAAM,UAAU,CAAC;IACnB;IAEA,GAAG,aAAa,CAAC,SAAS;AAC5B;AAEA,OAAO,OAAO,GAAG;IACf,YAAY,EAAE;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/json/output-json.js"], "sourcesContent": ["'use strict'\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFile } = require('../output-file')\n\nasync function outputJson (file, data, options = {}) {\n  const str = stringify(data, options)\n\n  await outputFile(file, str, options)\n}\n\nmodule.exports = outputJson\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM,EAAE,UAAU,EAAE;AAEpB,eAAe,WAAY,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACjD,MAAM,MAAM,UAAU,MAAM;IAE5B,MAAM,WAAW,MAAM,KAAK;AAC9B;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2089, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/json/output-json-sync.js"], "sourcesContent": ["'use strict'\n\nconst { stringify } = require('jsonfile/utils')\nconst { outputFileSync } = require('../output-file')\n\nfunction outputJsonSync (file, data, options) {\n  const str = stringify(data, options)\n\n  outputFileSync(file, str, options)\n}\n\nmodule.exports = outputJsonSync\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,EAAE,SAAS,EAAE;AACnB,MAAM,EAAE,cAAc,EAAE;AAExB,SAAS,eAAgB,IAAI,EAAE,IAAI,EAAE,OAAO;IAC1C,MAAM,MAAM,UAAU,MAAM;IAE5B,eAAe,MAAM,KAAK;AAC5B;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/json/index.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nconst jsonFile = require('./jsonfile')\n\njsonFile.outputJson = u(require('./output-json'))\njsonFile.outputJsonSync = require('./output-json-sync')\n// aliases\njsonFile.outputJSON = jsonFile.outputJson\njsonFile.outputJSONSync = jsonFile.outputJsonSync\njsonFile.writeJSON = jsonFile.writeJson\njsonFile.writeJSONSync = jsonFile.writeJsonSync\njsonFile.readJSON = jsonFile.readJson\njsonFile.readJSONSync = jsonFile.readJsonSync\n\nmodule.exports = jsonFile\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,MAAM;AAEN,SAAS,UAAU,GAAG;AACtB,SAAS,cAAc;AACvB,UAAU;AACV,SAAS,UAAU,GAAG,SAAS,UAAU;AACzC,SAAS,cAAc,GAAG,SAAS,cAAc;AACjD,SAAS,SAAS,GAAG,SAAS,SAAS;AACvC,SAAS,aAAa,GAAG,SAAS,aAAa;AAC/C,SAAS,QAAQ,GAAG,SAAS,QAAQ;AACrC,SAAS,YAAY,GAAG,SAAS,YAAY;AAE7C,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/move/move.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('../fs')\nconst path = require('path')\nconst { copy } = require('../copy')\nconst { remove } = require('../remove')\nconst { mkdirp } = require('../mkdirs')\nconst { pathExists } = require('../path-exists')\nconst stat = require('../util/stat')\n\nasync function move (src, dest, opts = {}) {\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  const { srcStat, isChangingCase = false } = await stat.checkPaths(src, dest, 'move', opts)\n\n  await stat.checkParentPaths(src, srcStat, dest, 'move')\n\n  // If the parent of dest is not root, make sure it exists before proceeding\n  const destParent = path.dirname(dest)\n  const parsedParentPath = path.parse(destParent)\n  if (parsedParentPath.root !== destParent) {\n    await mkdirp(destParent)\n  }\n\n  return doRename(src, dest, overwrite, isChangingCase)\n}\n\nasync function doRename (src, dest, overwrite, isChangingCase) {\n  if (!isChangingCase) {\n    if (overwrite) {\n      await remove(dest)\n    } else if (await pathExists(dest)) {\n      throw new Error('dest already exists.')\n    }\n  }\n\n  try {\n    // Try w/ rename first, and try copy + remove if EXDEV\n    await fs.rename(src, dest)\n  } catch (err) {\n    if (err.code !== 'EXDEV') {\n      throw err\n    }\n    await moveAcrossDevice(src, dest, overwrite)\n  }\n}\n\nasync function moveAcrossDevice (src, dest, overwrite) {\n  const opts = {\n    overwrite,\n    errorOnExist: true,\n    preserveTimestamps: true\n  }\n\n  await copy(src, dest, opts)\n  return remove(src)\n}\n\nmodule.exports = move\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,IAAI,EAAE;AACd,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,MAAM,EAAE;AAChB,MAAM,EAAE,UAAU,EAAE;AACpB,MAAM;AAEN,eAAe,KAAM,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,YAAY,KAAK,SAAS,IAAI,KAAK,OAAO,IAAI;IAEpD,MAAM,EAAE,OAAO,EAAE,iBAAiB,KAAK,EAAE,GAAG,MAAM,KAAK,UAAU,CAAC,KAAK,MAAM,QAAQ;IAErF,MAAM,KAAK,gBAAgB,CAAC,KAAK,SAAS,MAAM;IAEhD,2EAA2E;IAC3E,MAAM,aAAa,KAAK,OAAO,CAAC;IAChC,MAAM,mBAAmB,KAAK,KAAK,CAAC;IACpC,IAAI,iBAAiB,IAAI,KAAK,YAAY;QACxC,MAAM,OAAO;IACf;IAEA,OAAO,SAAS,KAAK,MAAM,WAAW;AACxC;AAEA,eAAe,SAAU,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc;IAC3D,IAAI,CAAC,gBAAgB;QACnB,IAAI,WAAW;YACb,MAAM,OAAO;QACf,OAAO,IAAI,MAAM,WAAW,OAAO;YACjC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,IAAI;QACF,sDAAsD;QACtD,MAAM,GAAG,MAAM,CAAC,KAAK;IACvB,EAAE,OAAO,KAAK;QACZ,IAAI,IAAI,IAAI,KAAK,SAAS;YACxB,MAAM;QACR;QACA,MAAM,iBAAiB,KAAK,MAAM;IACpC;AACF;AAEA,eAAe,iBAAkB,GAAG,EAAE,IAAI,EAAE,SAAS;IACnD,MAAM,OAAO;QACX;QACA,cAAc;QACd,oBAAoB;IACtB;IAEA,MAAM,KAAK,KAAK,MAAM;IACtB,OAAO,OAAO;AAChB;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/move/move-sync.js"], "sourcesContent": ["'use strict'\n\nconst fs = require('graceful-fs')\nconst path = require('path')\nconst copySync = require('../copy').copySync\nconst removeSync = require('../remove').removeSync\nconst mkdirpSync = require('../mkdirs').mkdirpSync\nconst stat = require('../util/stat')\n\nfunction moveSync (src, dest, opts) {\n  opts = opts || {}\n  const overwrite = opts.overwrite || opts.clobber || false\n\n  const { srcStat, isChangingCase = false } = stat.checkPathsSync(src, dest, 'move', opts)\n  stat.checkParentPathsSync(src, srcStat, dest, 'move')\n  if (!isParentRoot(dest)) mkdirpSync(path.dirname(dest))\n  return doRename(src, dest, overwrite, isChangingCase)\n}\n\nfunction isParentRoot (dest) {\n  const parent = path.dirname(dest)\n  const parsedPath = path.parse(parent)\n  return parsedPath.root === parent\n}\n\nfunction doRename (src, dest, overwrite, isChangingCase) {\n  if (isChangingCase) return rename(src, dest, overwrite)\n  if (overwrite) {\n    removeSync(dest)\n    return rename(src, dest, overwrite)\n  }\n  if (fs.existsSync(dest)) throw new Error('dest already exists.')\n  return rename(src, dest, overwrite)\n}\n\nfunction rename (src, dest, overwrite) {\n  try {\n    fs.renameSync(src, dest)\n  } catch (err) {\n    if (err.code !== 'EXDEV') throw err\n    return moveAcrossDevice(src, dest, overwrite)\n  }\n}\n\nfunction moveAcrossDevice (src, dest, overwrite) {\n  const opts = {\n    overwrite,\n    errorOnExist: true,\n    preserveTimestamps: true\n  }\n  copySync(src, dest, opts)\n  return removeSync(src)\n}\n\nmodule.exports = moveSync\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,WAAW,sGAAmB,QAAQ;AAC5C,MAAM,aAAa,wGAAqB,UAAU;AAClD,MAAM,aAAa,wGAAqB,UAAU;AAClD,MAAM;AAEN,SAAS,SAAU,GAAG,EAAE,IAAI,EAAE,IAAI;IAChC,OAAO,QAAQ,CAAC;IAChB,MAAM,YAAY,KAAK,SAAS,IAAI,KAAK,OAAO,IAAI;IAEpD,MAAM,EAAE,OAAO,EAAE,iBAAiB,KAAK,EAAE,GAAG,KAAK,cAAc,CAAC,KAAK,MAAM,QAAQ;IACnF,KAAK,oBAAoB,CAAC,KAAK,SAAS,MAAM;IAC9C,IAAI,CAAC,aAAa,OAAO,WAAW,KAAK,OAAO,CAAC;IACjD,OAAO,SAAS,KAAK,MAAM,WAAW;AACxC;AAEA,SAAS,aAAc,IAAI;IACzB,MAAM,SAAS,KAAK,OAAO,CAAC;IAC5B,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,OAAO,WAAW,IAAI,KAAK;AAC7B;AAEA,SAAS,SAAU,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc;IACrD,IAAI,gBAAgB,OAAO,OAAO,KAAK,MAAM;IAC7C,IAAI,WAAW;QACb,WAAW;QACX,OAAO,OAAO,KAAK,MAAM;IAC3B;IACA,IAAI,GAAG,UAAU,CAAC,OAAO,MAAM,IAAI,MAAM;IACzC,OAAO,OAAO,KAAK,MAAM;AAC3B;AAEA,SAAS,OAAQ,GAAG,EAAE,IAAI,EAAE,SAAS;IACnC,IAAI;QACF,GAAG,UAAU,CAAC,KAAK;IACrB,EAAE,OAAO,KAAK;QACZ,IAAI,IAAI,IAAI,KAAK,SAAS,MAAM;QAChC,OAAO,iBAAiB,KAAK,MAAM;IACrC;AACF;AAEA,SAAS,iBAAkB,GAAG,EAAE,IAAI,EAAE,SAAS;IAC7C,MAAM,OAAO;QACX;QACA,cAAc;QACd,oBAAoB;IACtB;IACA,SAAS,KAAK,MAAM;IACpB,OAAO,WAAW;AACpB;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/move/index.js"], "sourcesContent": ["'use strict'\n\nconst u = require('universalify').fromPromise\nmodule.exports = {\n  move: u(require('./move')),\n  moveSync: require('./move-sync')\n}\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,IAAI,iGAAwB,WAAW;AAC7C,OAAO,OAAO,GAAG;IACf,MAAM;IACN,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/fs-extra/lib/index.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = {\n  // Export promiseified graceful-fs:\n  ...require('./fs'),\n  // Export extra methods:\n  ...require('./copy'),\n  ...require('./empty'),\n  ...require('./ensure'),\n  ...require('./json'),\n  ...require('./mkdirs'),\n  ...require('./move'),\n  ...require('./output-file'),\n  ...require('./path-exists'),\n  ...require('./remove')\n}\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG;0GAEf;4GAEA;6GACA;8GACA;4GACA;8GACA;4GACA;mHACA;mHACA;8GACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/jsonfile/utils.js"], "sourcesContent": ["function stringify (obj, { EOL = '\\n', finalEOL = true, replacer = null, spaces } = {}) {\n  const EOF = finalEOL ? EOL : ''\n  const str = JSON.stringify(obj, replacer, spaces)\n\n  return str.replace(/\\n/g, EOL) + EOF\n}\n\nfunction stripBom (content) {\n  // we do this because JSON.parse would convert it to a utf8 string if encoding wasn't specified\n  if (Buffer.isBuffer(content)) content = content.toString('utf8')\n  return content.replace(/^\\uFEFF/, '')\n}\n\nmodule.exports = { stringify, stripBom }\n"], "names": [], "mappings": "AAAA,SAAS,UAAW,GAAG,EAAE,EAAE,MAAM,IAAI,EAAE,WAAW,IAAI,EAAE,WAAW,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;IACpF,MAAM,MAAM,WAAW,MAAM;IAC7B,MAAM,MAAM,KAAK,SAAS,CAAC,KAAK,UAAU;IAE1C,OAAO,IAAI,OAAO,CAAC,OAAO,OAAO;AACnC;AAEA,SAAS,SAAU,OAAO;IACxB,+FAA+F;IAC/F,IAAI,OAAO,QAAQ,CAAC,UAAU,UAAU,QAAQ,QAAQ,CAAC;IACzD,OAAO,QAAQ,OAAO,CAAC,WAAW;AACpC;AAEA,OAAO,OAAO,GAAG;IAAE;IAAW;AAAS", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Dev/test/self-improving-app/node_modules/jsonfile/index.js"], "sourcesContent": ["let _fs\ntry {\n  _fs = require('graceful-fs')\n} catch (_) {\n  _fs = require('fs')\n}\nconst universalify = require('universalify')\nconst { stringify, stripBom } = require('./utils')\n\nasync function _readFile (file, options = {}) {\n  if (typeof options === 'string') {\n    options = { encoding: options }\n  }\n\n  const fs = options.fs || _fs\n\n  const shouldThrow = 'throws' in options ? options.throws : true\n\n  let data = await universalify.fromCallback(fs.readFile)(file, options)\n\n  data = stripBom(data)\n\n  let obj\n  try {\n    obj = JSON.parse(data, options ? options.reviver : null)\n  } catch (err) {\n    if (shouldThrow) {\n      err.message = `${file}: ${err.message}`\n      throw err\n    } else {\n      return null\n    }\n  }\n\n  return obj\n}\n\nconst readFile = universalify.fromPromise(_readFile)\n\nfunction readFileSync (file, options = {}) {\n  if (typeof options === 'string') {\n    options = { encoding: options }\n  }\n\n  const fs = options.fs || _fs\n\n  const shouldThrow = 'throws' in options ? options.throws : true\n\n  try {\n    let content = fs.readFileSync(file, options)\n    content = stripBom(content)\n    return JSON.parse(content, options.reviver)\n  } catch (err) {\n    if (shouldThrow) {\n      err.message = `${file}: ${err.message}`\n      throw err\n    } else {\n      return null\n    }\n  }\n}\n\nasync function _writeFile (file, obj, options = {}) {\n  const fs = options.fs || _fs\n\n  const str = stringify(obj, options)\n\n  await universalify.fromCallback(fs.writeFile)(file, str, options)\n}\n\nconst writeFile = universalify.fromPromise(_writeFile)\n\nfunction writeFileSync (file, obj, options = {}) {\n  const fs = options.fs || _fs\n\n  const str = stringify(obj, options)\n  // not sure if fs.writeFileSync returns anything, but just in case\n  return fs.writeFileSync(file, str, options)\n}\n\nconst jsonfile = {\n  readFile,\n  readFileSync,\n  writeFile,\n  writeFileSync\n}\n\nmodule.exports = jsonfile\n"], "names": [], "mappings": "AAAA,IAAI;AACJ,IAAI;IACF;AACF,EAAE,OAAO,GAAG;IACV;AACF;AACA,MAAM;AACN,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE;AAE7B,eAAe,UAAW,IAAI,EAAE,UAAU,CAAC,CAAC;IAC1C,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YAAE,UAAU;QAAQ;IAChC;IAEA,MAAM,KAAK,QAAQ,EAAE,IAAI;IAEzB,MAAM,cAAc,YAAY,UAAU,QAAQ,MAAM,GAAG;IAE3D,IAAI,OAAO,MAAM,aAAa,YAAY,CAAC,GAAG,QAAQ,EAAE,MAAM;IAE9D,OAAO,SAAS;IAEhB,IAAI;IACJ,IAAI;QACF,MAAM,KAAK,KAAK,CAAC,MAAM,UAAU,QAAQ,OAAO,GAAG;IACrD,EAAE,OAAO,KAAK;QACZ,IAAI,aAAa;YACf,IAAI,OAAO,GAAG,GAAG,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACvC,MAAM;QACR,OAAO;YACL,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,MAAM,WAAW,aAAa,WAAW,CAAC;AAE1C,SAAS,aAAc,IAAI,EAAE,UAAU,CAAC,CAAC;IACvC,IAAI,OAAO,YAAY,UAAU;QAC/B,UAAU;YAAE,UAAU;QAAQ;IAChC;IAEA,MAAM,KAAK,QAAQ,EAAE,IAAI;IAEzB,MAAM,cAAc,YAAY,UAAU,QAAQ,MAAM,GAAG;IAE3D,IAAI;QACF,IAAI,UAAU,GAAG,YAAY,CAAC,MAAM;QACpC,UAAU,SAAS;QACnB,OAAO,KAAK,KAAK,CAAC,SAAS,QAAQ,OAAO;IAC5C,EAAE,OAAO,KAAK;QACZ,IAAI,aAAa;YACf,IAAI,OAAO,GAAG,GAAG,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE;YACvC,MAAM;QACR,OAAO;YACL,OAAO;QACT;IACF;AACF;AAEA,eAAe,WAAY,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;IAChD,MAAM,KAAK,QAAQ,EAAE,IAAI;IAEzB,MAAM,MAAM,UAAU,KAAK;IAE3B,MAAM,aAAa,YAAY,CAAC,GAAG,SAAS,EAAE,MAAM,KAAK;AAC3D;AAEA,MAAM,YAAY,aAAa,WAAW,CAAC;AAE3C,SAAS,cAAe,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,CAAC;IAC7C,MAAM,KAAK,QAAQ,EAAE,IAAI;IAEzB,MAAM,MAAM,UAAU,KAAK;IAC3B,kEAAkE;IAClE,OAAO,GAAG,aAAa,CAAC,MAAM,KAAK;AACrC;AAEA,MAAM,WAAW;IACf;IACA;IACA;IACA;AACF;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}]}