import * as acorn from 'acorn';
import { parse as babelParse } from '@babel/parser';
import { fileSystem, FileInfo } from './filesystem';
import { aiClient, CodeAnalysisResult, CodeSuggestion } from './ai-client';

export interface ProjectAnalysis {
  files: FileAnalysis[];
  overallMetrics: ProjectMetrics;
  suggestions: CodeSuggestion[];
  lastAnalyzed: Date;
}

export interface FileAnalysis {
  file: FileInfo;
  analysis: CodeAnalysisResult;
  ast?: any;
  dependencies: string[];
  exports: string[];
}

export interface ProjectMetrics {
  totalFiles: number;
  totalLinesOfCode: number;
  averageComplexity: number;
  averageMaintainability: number;
  issueCount: number;
  suggestionCount: number;
  testCoverage?: number;
}

export class CodeAnalyzer {
  private analysisCache: Map<string, FileAnalysis> = new Map();

  async analyzeProject(): Promise<ProjectAnalysis> {
    console.log('Starting project analysis...');
    
    const files = await fileSystem.listDirectory('src', true);
    const codeFiles = files.filter(file => 
      file.type === 'file' && this.isAnalyzableFile(file.name)
    );

    const fileAnalyses: FileAnalysis[] = [];
    const allSuggestions: CodeSuggestion[] = [];

    for (const file of codeFiles) {
      try {
        const analysis = await this.analyzeFile(file);
        fileAnalyses.push(analysis);
        allSuggestions.push(...analysis.analysis.suggestions);
      } catch (error) {
        console.error(`Error analyzing file ${file.path}:`, error);
      }
    }

    const overallMetrics = this.calculateProjectMetrics(fileAnalyses);

    return {
      files: fileAnalyses,
      overallMetrics,
      suggestions: allSuggestions,
      lastAnalyzed: new Date()
    };
  }

  async analyzeFile(file: FileInfo): Promise<FileAnalysis> {
    // Check cache first
    const cacheKey = `${file.path}_${file.lastModified?.getTime()}`;
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)!;
    }

    console.log(`Analyzing file: ${file.path}`);

    const content = file.content || await fileSystem.readFile(file.path);
    
    // Parse AST for JavaScript/TypeScript files
    let ast: any = null;
    let dependencies: string[] = [];
    let exports: string[] = [];

    if (this.isJavaScriptFile(file.name)) {
      try {
        ast = this.parseAST(content, file.name);
        dependencies = this.extractDependencies(ast);
        exports = this.extractExports(ast);
      } catch (error) {
        console.warn(`Could not parse AST for ${file.path}:`, error);
        // Continue without AST - the AI analysis will still work
        ast = null;
        dependencies = [];
        exports = [];
      }
    }

    // Get AI analysis
    const analysis = await aiClient.analyzeCode(content, file.path);

    const fileAnalysis: FileAnalysis = {
      file: { ...file, content },
      analysis,
      ast,
      dependencies,
      exports
    };

    // Cache the result
    this.analysisCache.set(cacheKey, fileAnalysis);

    return fileAnalysis;
  }

  async generateImprovements(
    file: FileInfo, 
    improvementTypes: string[] = ['performance', 'refactor', 'style']
  ): Promise<CodeSuggestion[]> {
    const content = file.content || await fileSystem.readFile(file.path);
    const suggestions: CodeSuggestion[] = [];

    for (const type of improvementTypes) {
      try {
        const suggestion = await aiClient.generateImprovement(
          content, 
          file.path, 
          type,
          `Improve ${type} aspects of this code`
        );
        
        if (suggestion) {
          suggestions.push(suggestion);
        }
      } catch (error) {
        console.error(`Error generating ${type} improvement for ${file.path}:`, error);
      }
    }

    return suggestions;
  }

  async generateNewFeature(description: string, targetFile?: string): Promise<CodeSuggestion | null> {
    let contextFile = targetFile;
    let contextCode = '';

    if (!contextFile) {
      // Find the most relevant file based on the description
      const files = await fileSystem.listDirectory('src', true);
      const mainFiles = files.filter(f => 
        f.name.includes('page.tsx') || 
        f.name.includes('index.tsx') ||
        f.name.includes('App.tsx')
      );
      
      contextFile = mainFiles[0]?.path || 'src/app/page.tsx';
    }

    try {
      contextCode = await fileSystem.readFile(contextFile);
    } catch (error) {
      console.warn(`Could not read context file ${contextFile}:`, error);
    }

    return await aiClient.generateNewFeature(description, contextCode, contextFile);
  }

  private parseAST(code: string, fileName: string): any {
    const isTypeScript = fileName.endsWith('.ts') || fileName.endsWith('.tsx');
    const isJSX = fileName.endsWith('.jsx') || fileName.endsWith('.tsx');

    if (isTypeScript) {
      // Use Babel parser for TypeScript files
      try {
        return babelParse(code, {
          sourceType: 'module',
          allowImportExportEverywhere: true,
          allowReturnOutsideFunction: true,
          plugins: [
            'typescript',
            ...(isJSX ? ['jsx'] : []),
            'decorators-legacy',
            'classProperties',
            'objectRestSpread',
            'asyncGenerators',
            'functionBind',
            'exportDefaultFrom',
            'exportNamespaceFrom',
            'dynamicImport',
            'nullishCoalescingOperator',
            'optionalChaining'
          ]
        });
      } catch (error) {
        throw new Error(`Could not parse TypeScript file ${fileName}: ${error}`);
      }
    } else {
      // Use Acorn for JavaScript files
      try {
        return acorn.parse(code, {
          ecmaVersion: 2022,
          sourceType: 'module',
          allowImportExportEverywhere: true,
          allowReturnOutsideFunction: true,
        });
      } catch (error) {
        // If parsing fails, try with different options
        try {
          return acorn.parse(code, {
            ecmaVersion: 2022,
            sourceType: 'script',
          });
        } catch (secondError) {
          throw new Error(`Could not parse JavaScript file ${fileName}: ${error}`);
        }
      }
    }
  }

  private extractDependencies(ast: any): string[] {
    const dependencies: string[] = [];
    
    if (!ast || !ast.body) return dependencies;

    for (const node of ast.body) {
      if (node.type === 'ImportDeclaration' && node.source?.value) {
        dependencies.push(node.source.value);
      }
    }

    return [...new Set(dependencies)]; // Remove duplicates
  }

  private extractExports(ast: any): string[] {
    const exports: string[] = [];
    
    if (!ast || !ast.body) return exports;

    for (const node of ast.body) {
      if (node.type === 'ExportNamedDeclaration') {
        if (node.declaration) {
          if (node.declaration.type === 'FunctionDeclaration' && node.declaration.id) {
            exports.push(node.declaration.id.name);
          } else if (node.declaration.type === 'VariableDeclaration') {
            for (const declarator of node.declaration.declarations) {
              if (declarator.id?.name) {
                exports.push(declarator.id.name);
              }
            }
          }
        }
        
        if (node.specifiers) {
          for (const specifier of node.specifiers) {
            if (specifier.exported?.name) {
              exports.push(specifier.exported.name);
            }
          }
        }
      } else if (node.type === 'ExportDefaultDeclaration') {
        exports.push('default');
      }
    }

    return [...new Set(exports)]; // Remove duplicates
  }

  private calculateProjectMetrics(fileAnalyses: FileAnalysis[]): ProjectMetrics {
    const totalFiles = fileAnalyses.length;
    let totalLinesOfCode = 0;
    let totalComplexity = 0;
    let totalMaintainability = 0;
    let issueCount = 0;
    let suggestionCount = 0;

    for (const analysis of fileAnalyses) {
      const metrics = analysis.analysis.metrics;
      totalLinesOfCode += metrics.linesOfCode;
      totalComplexity += metrics.complexity;
      totalMaintainability += metrics.maintainability;
      issueCount += analysis.analysis.issues.length;
      suggestionCount += analysis.analysis.suggestions.length;
    }

    return {
      totalFiles,
      totalLinesOfCode,
      averageComplexity: totalFiles > 0 ? totalComplexity / totalFiles : 0,
      averageMaintainability: totalFiles > 0 ? totalMaintainability / totalFiles : 0,
      issueCount,
      suggestionCount
    };
  }

  private isAnalyzableFile(fileName: string): boolean {
    const analyzableExtensions = ['.js', '.jsx', '.ts', '.tsx', '.css', '.json'];
    return analyzableExtensions.some(ext => fileName.endsWith(ext));
  }

  private isJavaScriptFile(fileName: string): boolean {
    return fileName.endsWith('.js') || 
           fileName.endsWith('.jsx') || 
           fileName.endsWith('.ts') || 
           fileName.endsWith('.tsx');
  }

  clearCache(): void {
    this.analysisCache.clear();
  }

  getCacheSize(): number {
    return this.analysisCache.size;
  }
}

export const codeAnalyzer = new CodeAnalyzer();
