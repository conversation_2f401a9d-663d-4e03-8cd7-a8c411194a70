const CHUNK_PUBLIC_PATH = "server/app/api/analyze/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_8b2a0559._.js");
runtime.loadChunk("server/chunks/node_modules_acorn_dist_acorn_mjs_69fe48d7._.js");
runtime.loadChunk("server/chunks/node_modules_openai_1a6eb0e6._.js");
runtime.loadChunk("server/chunks/node_modules_b6e2c493._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__21ca7e64._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/analyze/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/analyze/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
